# Augment2API

Augment2API 是一个用于与 Augment API 进行交互的 TypeScript 客户端工具。它实现了 OAuth 认证流程，并提供了与 Augment 聊天 API 进行通信的功能。

## 功能特性

- OAuth 2.0 认证流程（PKCE 流程）
- 生成授权 URL
- 获取访问令牌
- 与 Augment 聊天 API 进行交互
- 处理服务器发送的事件流

## 安装

```bash
# 克隆仓库
git clone <repository-url>
cd augment2api

# 安装依赖
npm install
```

## 使用方法

### 配置

在使用前，你需要设置正确的 OAuth 客户端 ID 和重定向 URI。

### 运行

```bash
# 开发模式运行
npm run dev

# 或者直接启动
npm start
```

### 构建

```bash
# 编译 TypeScript 代码
npm run build
```

## 工作流程

1. 生成 OAuth 状态和 PKCE 挑战码
2. 打开授权 URL 进行认证
3. 输入授权码
4. 获取访问令牌
5. 使用访问令牌与 Augment 聊天 API 交互

## 注意事项

- 确保在使用前配置正确的客户端 ID
- 网络请求可能需要代理设置，具体取决于你的网络环境 