// ==UserScript==
// @name         AugmentCode Token Helper Final
// @namespace    http://tampermonkey.net/
// @version      1.3
// @description  最终版AugmentCode授权码和Token获取工具
// <AUTHOR>
// @match        https://*.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_registerMenuCommand
// @grant        GM_openInTab
// ==/UserScript==

(function() {
    'use strict';

    // 控制台日志函数
    function log(message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] [AugmentCode] ${message}`, data || '');
    }

    // PKCE相关函数
    function base64URLEncode(buffer) {
        return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    async function sha256Hash(input) {
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return hashBuffer;
    }

    async function createOAuthState() {
        log('开始创建OAuth状态...');
        
        const codeVerifierArray = new Uint8Array(32);
        crypto.getRandomValues(codeVerifierArray);
        const codeVerifier = base64URLEncode(codeVerifierArray.buffer);
        
        const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));
        
        const stateArray = new Uint8Array(8);
        crypto.getRandomValues(stateArray);
        const state = base64URLEncode(stateArray.buffer);

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: Date.now()
        };

        log('OAuth状态创建完成', oauthState);
        GM_setValue('oauthState', JSON.stringify(oauthState));
        return oauthState;
    }

    // 解析授权码
    function parseCode(code) {
        log('开始解析授权码...', { inputCode: code });
        
        try {
            const parsed = JSON.parse(code);
            const result = {
                code: parsed.code,
                state: parsed.state,
                tenant_url: parsed.tenant_url,
            };
            
            log('授权码解析成功', result);
            return result;
        } catch (error) {
            log('授权码解析失败', { error: error.message, inputCode: code });
            throw new Error(`授权码解析失败: ${error.message}`);
        }
    }

    // 获取token函数
    async function getAccessToken(tenant_url, codeVerifier, code) {
        log('开始获取访问令牌...', { tenant_url, codeVerifier, code });
        
        const clientID = "v";
        const data = {
            grant_type: "authorization_code",
            client_id: clientID,
            code_verifier: codeVerifier,
            redirect_uri: "",
            code: code
        };

        log('准备发送token请求', { endpoint: `${tenant_url}token`, data });

        try {
            const result = await new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: "POST",
                    url: `${tenant_url}token`,
                    data: JSON.stringify(data),
                    headers: {
                        "Content-Type": "application/json"
                    },
                    onload: function(response) {
                        log('收到token响应', { 
                            status: response.status, 
                            responseText: response.responseText 
                        });
                        
                        if (response.status === 200) {
                            try {
                                const json = JSON.parse(response.responseText);
                                if (json.access_token) {
                                    log('Token获取成功', { token: json.access_token });
                                    resolve(json.access_token);
                                } else {
                                    log('响应中没有access_token', json);
                                    reject(new Error(`No access_token in response: ${response.responseText}`));
                                }
                            } catch (error) {
                                log('JSON解析错误', { error: error.message });
                                reject(new Error(`JSON parse error: ${error.message}`));
                            }
                        } else {
                            log('HTTP请求失败', { status: response.status, responseText: response.responseText });
                            reject(new Error(`HTTP ${response.status}: ${response.responseText}`));
                        }
                    },
                    onerror: function(error) {
                        log('网络请求错误', error);
                        reject(new Error(`Network error: ${error}`));
                    }
                });
            });

            return result;
        } catch (error) {
            log('Token获取失败', { error: error.message });
            throw error;
        }
    }

    // 生成授权链接
    async function generateAuthorizationUrl() {
        log('开始生成授权链接...');
        
        try {
            const oauthState = await createOAuthState();
            const baseUrl = 'https://auth.augmentcode.com/authorize';
            const params = new URLSearchParams({
                client_id: 'v',
                response_type: 'code',
                redirect_uri: '',
                code_challenge: oauthState.codeChallenge,
                code_challenge_method: 'S256',
                state: oauthState.state
            });

            const authUrl = `${baseUrl}?${params.toString()}`;
            log('授权链接生成完成', { authUrl });
            return authUrl;
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            throw error;
        }
    }

    // 处理菜单命令：生成授权链接
    async function handleGenerateAuthLink() {
        try {
            const authUrl = await generateAuthorizationUrl();
            GM_openInTab(authUrl, true);
            log('授权链接已在新标签页打开');
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            alert(`生成授权链接失败: ${error.message}`);
        }
    }

    // 直接提取data变量的内容
    function extractAuthCode() {
        log('=== 直接提取data变量内容 ===');

        // 获取所有script标签
        const scripts = document.querySelectorAll('script');
        log('找到script标签数量:', scripts.length);

        // 强制测试返回
        const testReturn = '{"code":"test","state":"test","tenant_url":"test"}';
        log('测试返回值:', testReturn);
        // return testReturn; // 取消注释来测试返回机制

        for (let i = 0; i < scripts.length; i++) {
            const script = scripts[i];
            if (script.textContent) {
                const content = script.textContent;

                // 查找 data = {...} 模式，支持多行
                const dataMatch = content.match(/data\s*=\s*\{[^}]*code[^}]*\}/s);
                if (dataMatch) {
                    log(`在script ${i}中找到data变量:`, dataMatch[0]);

                    const dataStr = dataMatch[0].replace('data = ', '');
                    log('提取的data内容:', dataStr);

                    // 转换JavaScript对象格式为JSON格式
                    try {
                        // 将JavaScript对象格式转换为JSON格式
                        let jsonStr = dataStr
                            .replace(/\s+/g, ' ')         // 压缩空白字符
                            .replace(/(\w+):/g, '"$1":')  // 给属性名加引号
                            .replace(/,\s*}/g, '}')       // 移除末尾逗号
                            .trim();                      // 移除首尾空格

                        log('转换后的JSON:', jsonStr);

                        // 验证是否为有效JSON
                        const parsed = JSON.parse(jsonStr);
                        log('JSON验证成功:', parsed);

                        // 重新序列化为紧凑格式
                        const compactJson = JSON.stringify(parsed);
                        log('紧凑JSON格式:', compactJson);

                        return compactJson;
                    } catch (e) {
                        log('JSON转换失败，尝试手动提取:', e.message);

                        // 手动提取各个字段
                        const codeMatch = dataStr.match(/code:\s*"([^"]+)"/);
                        const stateMatch = dataStr.match(/state:\s*"([^"]+)"/);
                        const tenantMatch = dataStr.match(/tenant_url:\s*"([^"]+)"/);

                        if (codeMatch && stateMatch && tenantMatch) {
                            const manualJson = JSON.stringify({
                                code: codeMatch[1],
                                state: stateMatch[1],
                                tenant_url: tenantMatch[1]
                            });
                            log('手动构造的JSON:', manualJson);
                            return manualJson;
                        }

                        log('手动提取也失败，返回原始内容');
                        return dataStr;
                    }
                }

                // 如果没找到data变量，继续用原来的方法
                if (content.includes('code')) {
                    log(`检查script ${i}是否包含code...`);

                    // 直接搜索code值
                    const codeMatch = content.match(/"code"\s*:\s*"([^"]+)"/);
                    if (codeMatch) {
                        const code = codeMatch[1];
                        log('找到code值:', code);

                        // 搜索state值
                        const stateMatch = content.match(/"state"\s*:\s*"([^"]+)"/);
                        const state = stateMatch ? stateMatch[1] : 'default_state';
                        log('找到state值:', state);

                        // 搜索tenant_url值
                        const tenantMatch = content.match(/"tenant_url"\s*:\s*"([^"]+)"/);
                        const tenant_url = tenantMatch ? tenantMatch[1] : window.location.origin + '/';
                        log('找到tenant_url值:', tenant_url);

                        // 构造结果
                        const result = {
                            code: code,
                            state: state,
                            tenant_url: tenant_url
                        };

                        const resultJson = JSON.stringify(result);
                        log('构造的授权码JSON:', resultJson);

                        return resultJson;
                    }
                }
            }
        }

        log('未找到授权码');
        return null;
    }

    // 自动获取Token流程
    async function autoGetToken(authCode) {
        log('开始自动Token获取流程...', { authCode });
        
        try {
            const parsedCode = parseCode(authCode);
            const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
            
            if (!savedOAuthState.codeVerifier) {
                throw new Error('未找到保存的Code Verifier，请先生成授权链接');
            }
            
            log('找到保存的OAuth状态', savedOAuthState);
            
            // 不验证state，因为可能不匹配
            log('跳过State验证');
            
            const token = await getAccessToken(
                parsedCode.tenant_url,
                savedOAuthState.codeVerifier,
                parsedCode.code
            );
            
            log('Token获取流程完成', { token });
            GM_deleteValue('oauthState');
            log('已清除保存的OAuth状态');
            
            return { token, parsedCode, success: true };
            
        } catch (error) {
            log('Token获取流程失败', { error: error.message });
            return { error: error.message, success: false };
        }
    }

    // 创建最终UI
    function createFinalUI() {
        log('开始创建最终UI...');

        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '10px';
        container.style.right = '10px';
        container.style.padding = '15px';
        container.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        container.style.border = '2px solid #007bff';
        container.style.borderRadius = '8px';
        container.style.zIndex = '10000';
        container.style.width = '350px';
        container.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
        container.style.fontFamily = 'Arial, sans-serif';

        const title = document.createElement('h3');
        title.textContent = 'AugmentCode Token Helper Final';
        title.style.margin = '0 0 15px 0';
        title.style.color = '#007bff';
        title.style.borderBottom = '2px solid #007bff';
        title.style.paddingBottom = '5px';
        container.appendChild(title);

        // OAuth状态显示
        const statusSection = document.createElement('div');
        statusSection.style.margin = '15px 0';
        statusSection.style.padding = '10px';
        statusSection.style.backgroundColor = '#e9ecef';
        statusSection.style.border = '1px solid #ced4da';
        statusSection.style.borderRadius = '4px';
        statusSection.style.fontSize = '12px';

        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
        if (savedOAuthState.codeVerifier) {
            statusSection.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#28a745;">已准备</span><br>
                <strong>State:</strong> ${savedOAuthState.state}<br>
                <strong>创建时间:</strong> ${new Date(savedOAuthState.creationTime).toLocaleString()}
            `;
        } else {
            statusSection.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#dc3545;">未准备</span><br>
                <small>请先通过菜单"生成授权链接"创建OAuth状态</small>
            `;
        }
        container.appendChild(statusSection);

        // 主要功能区域
        const mainSection = document.createElement('div');
        mainSection.style.margin = '15px 0';
        mainSection.style.padding = '10px';
        mainSection.style.backgroundColor = '#d4edda';
        mainSection.style.border = '1px solid #c3e6cb';
        mainSection.style.borderRadius = '4px';

        const mainTitle = document.createElement('h4');
        mainTitle.textContent = '授权码获取';
        mainTitle.style.margin = '0 0 10px 0';
        mainTitle.style.color = '#155724';
        mainSection.appendChild(mainTitle);

        const result = document.createElement('div');
        result.style.margin = '10px 0';
        result.style.wordBreak = 'break-all';
        result.style.fontSize = '12px';
        mainSection.appendChild(result);

        // 检测按钮
        const detectBtn = document.createElement('button');
        detectBtn.textContent = '从Script检测授权码';
        detectBtn.style.margin = '5px 5px 5px 0';
        detectBtn.style.padding = '8px 15px';
        detectBtn.style.backgroundColor = '#17a2b8';
        detectBtn.style.color = 'white';
        detectBtn.style.border = 'none';
        detectBtn.style.borderRadius = '4px';
        detectBtn.style.cursor = 'pointer';
        mainSection.appendChild(detectBtn);

        // 剪贴板按钮
        const clipboardBtn = document.createElement('button');
        clipboardBtn.textContent = '从剪贴板获取';
        clipboardBtn.style.margin = '5px';
        clipboardBtn.style.padding = '8px 15px';
        clipboardBtn.style.backgroundColor = '#ffc107';
        clipboardBtn.style.color = '#212529';
        clipboardBtn.style.border = 'none';
        clipboardBtn.style.borderRadius = '4px';
        clipboardBtn.style.cursor = 'pointer';
        mainSection.appendChild(clipboardBtn);

        container.appendChild(mainSection);

        // 检测按钮事件
        detectBtn.addEventListener('click', () => {
            log('用户点击Script检测按钮');
            result.innerHTML = '<span style="color:blue;">正在从Script检测...</span>';

            setTimeout(() => {
                const authCode = extractAuthCode();
                log('检测结果:', authCode);

                if (authCode) {
                    log('找到授权码，直接显示:', authCode);

                    // 直接显示授权码JSON，不解析
                    result.innerHTML = `
                        <strong>从Script检测到授权码:</strong><br>
                        <textarea readonly style="width:100%;height:60px;font-size:10px; margin:5px 0;">${authCode}</textarea><br>
                        <button id="getTokenBtn1" style="margin-top:10px; padding:8px 15px; background:#28a745; color:white; border:none; border-radius:4px; cursor:pointer;">获取Token</button>
                        <div id="tokenResult1" style="margin-top:10px;"></div>
                    `;

                    document.getElementById('getTokenBtn1').addEventListener('click', async () => {
                        const btn = document.getElementById('getTokenBtn1');
                        const tokenResult = document.getElementById('tokenResult1');

                        btn.disabled = true;
                        btn.textContent = '获取中...';

                        const result = await autoGetToken(authCode);

                        if (result.success) {
                            tokenResult.innerHTML = `
                                <div style="background:#d4edda; padding:10px; border:1px solid #c3e6cb; border-radius:4px;">
                                    <strong>Token获取成功!</strong><br>
                                    <textarea readonly style="width:100%;height:80px;font-size:10px; margin:5px 0;">${result.token}</textarea><br>
                                    <button onclick="navigator.clipboard.writeText('${result.token}');alert('已复制Token!')">复制Token</button>
                                </div>
                            `;
                        } else {
                            tokenResult.innerHTML = `
                                <div style="background:#f8d7da; padding:10px; border:1px solid #f5c6cb; border-radius:4px;">
                                    <strong>Token获取失败:</strong><br>
                                    <span style="color:red">${result.error}</span>
                                </div>
                            `;
                        }

                        btn.disabled = false;
                        btn.textContent = '获取Token';
                    });
                } else {
                    result.innerHTML = '<span style="color:#856404;">Script中未检测到授权码</span>';
                }
            }, 500);
        });

        // 剪贴板按钮事件
        clipboardBtn.addEventListener('click', async () => {
            try {
                const clipboardText = await navigator.clipboard.readText();
                log('从剪贴板读取内容', { clipboardText });

                const parsed = JSON.parse(clipboardText);
                if (parsed.code && parsed.state && parsed.tenant_url) {
                    log('剪贴板中找到有效授权码', parsed);

                    result.innerHTML = `
                        <strong>从剪贴板获取到授权码:</strong><br>
                        <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                        <strong>State:</strong> ${parsed.state}<br>
                        <strong>Tenant URL:</strong> ${parsed.tenant_url}<br>
                        <button id="getTokenBtn2" style="margin-top:10px; padding:8px 15px; background:#28a745; color:white; border:none; border-radius:4px; cursor:pointer;">获取Token</button>
                        <div id="tokenResult2" style="margin-top:10px;"></div>
                    `;

                    document.getElementById('getTokenBtn2').addEventListener('click', async () => {
                        const btn = document.getElementById('getTokenBtn2');
                        const tokenResult = document.getElementById('tokenResult2');

                        btn.disabled = true;
                        btn.textContent = '获取中...';

                        const result = await autoGetToken(clipboardText);

                        if (result.success) {
                            tokenResult.innerHTML = `
                                <div style="background:#d4edda; padding:10px; border:1px solid #c3e6cb; border-radius:4px;">
                                    <strong>Token获取成功!</strong><br>
                                    <textarea readonly style="width:100%;height:80px;font-size:10px; margin:5px 0;">${result.token}</textarea><br>
                                    <button onclick="navigator.clipboard.writeText('${result.token}');alert('已复制Token!')">复制Token</button>
                                </div>
                            `;
                        } else {
                            tokenResult.innerHTML = `
                                <div style="background:#f8d7da; padding:10px; border:1px solid #f5c6cb; border-radius:4px;">
                                    <strong>Token获取失败:</strong><br>
                                    <span style="color:red">${result.error}</span>
                                </div>
                            `;
                        }

                        btn.disabled = false;
                        btn.textContent = '获取Token';
                    });

                } else {
                    result.innerHTML = '<span style="color:red">剪贴板内容不是有效的授权码JSON</span>';
                }
            } catch (error) {
                log('从剪贴板获取失败', { error: error.message });
                result.innerHTML = `<span style="color:red">从剪贴板获取失败: ${error.message}</span>`;
            }
        });

        log('最终UI创建完成');
        return container;
    }

    // 注册菜单命令
    GM_registerMenuCommand('生成授权链接', handleGenerateAuthLink);

    // 页面加载完成后创建UI
    window.addEventListener('load', () => {
        log('页面加载完成，开始初始化');
        try {
            const ui = createFinalUI();
            document.body.appendChild(ui);
            log('最终版AugmentCode Token Helper已启动');
        } catch (error) {
            log('UI创建失败', { error: error.message });
            console.error('UI创建失败:', error);
        }
    });

})();
