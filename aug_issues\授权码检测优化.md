# 授权码检测优化任务

## 任务背景
用户反馈脚本 `augmentcode_token_helper_auto.user.js` 中的授权码获取机制需要优化，真实的授权码格式为：
```json
{"code":"_9e34c5ded380e449a860f8fab5e6593f","state":"xsT9x3NqnfI","tenant_url":"https://d13.api.augmentcode.com/"}
```

但脚本可能检测到的是JavaScript对象格式：
```javascript
{
  code: "_93a872b07f51569ba9e47eb76b03fed5",
  state: "xsT9x3NqnfI",
  tenant_url: "https://d13.api.augmentcode.com/",
}
```

## 修改方案
采用方案1：完全替换为Script标签专用检测，参考 `augmentcode_token_helper_final.user.js` 的实现。

## 执行的修改

### 1. 替换 `autoExtractCode()` 函数
**位置**: 第185-299行 → 第185-311行
**修改内容**:
- 移除了原有的5种复杂检测方法
- 专注于Script标签内容搜索
- 实现了3种检测策略：
  1. **data变量检测**: 查找 `data = {...}` 模式
  2. **标准JSON检测**: 直接搜索JSON格式
  3. **分散字段检测**: 分别提取各字段

### 2. 格式转换逻辑
**JavaScript对象 → JSON转换规则**:
```javascript
// 转换规则
.replace(/\s+/g, ' ')         // 压缩空白字符
.replace(/(\w+):/g, '"$1":')  // 给属性名加引号
.replace(/,\s*}/g, '}')       // 移除末尾逗号
.trim()                       // 移除首尾空格
```

**容错机制**:
- JSON.parse() 失败时启用手动字段提取
- 使用正则表达式分别匹配 code、state、tenant_url
- 构造标准JSON格式返回

### 3. 修改 `autoGetToken()` 函数
**位置**: 第327-334行 → 第327-330行
**修改内容**:
- 跳过State验证（因为自动检测的授权码可能与保存的state不匹配）
- 参考final版本的做法

## 技术实现细节

### 检测优先级
1. **data变量模式**: `data = {code: "...", state: "...", tenant_url: "..."}`
2. **标准JSON模式**: `{"code":"...","state":"...","tenant_url":"..."}`
3. **分散字段模式**: 分别搜索各字段值

### 错误处理
- 详细的控制台日志记录每个检测步骤
- 转换失败时的备用方案
- 字段缺失时的默认值处理

### 兼容性保证
- 函数名称保持 `autoExtractCode()`
- 返回值格式保持一致（JSON字符串或null）
- 现有UI调用逻辑无需修改

## 预期效果
1. **更准确的检测**: 专注于Script标签，提高检测成功率
2. **格式兼容**: 支持JavaScript对象和JSON两种格式
3. **详细调试**: 提供完整的检测过程日志
4. **容错能力**: 多层次的备用检测方案

## 测试场景
- [x] JavaScript对象格式检测
- [x] 标准JSON格式检测
- [x] 格式转换功能
- [x] 错误处理机制
- [x] UI兼容性

## 修改文件
- `augmentcode_token_helper_auto.user.js`: 主要修改文件
- `./aug_issues/授权码检测优化.md`: 任务记录文件

## 完成状态
✅ 已完成所有计划的修改
✅ 保持了向后兼容性
✅ 增强了错误处理和调试功能
