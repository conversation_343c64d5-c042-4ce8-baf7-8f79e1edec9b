import { randomBytes, createHash } from "crypto";
import * as readline from "node:readline/promises";
import { stdin as input, stdout as output } from "node:process";

const clientID = "v";

function base64URLEncode(buffer: Buffer): string {
  return buffer
    .toString("base64")
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=/g, "");
}

function sha256Hash(input: string | Buffer): Buffer {
  return createHash("sha256").update(input).digest();
}

function createOAuthState() {
  const codeVerifier = base64URLEncode(randomBytes(32));
  const codeChallenge = base64URLEncode(sha256Hash(Buffer.from(codeVerifier)));
  const state = base64URLEncode(randomBytes(8));

  const oauthState = {
    codeVerifier,
    codeChallenge,
    state,
    creationTime: Date.now(),
  };

  console.log(oauthState);
  return oauthState;
}

const generateAuthorizeURL = (oauthState: {
  codeVerifier: string;
  codeChallenge: string;
  state: string;
  creationTime: number;
}) => {
  const params = new URLSearchParams({
    response_type: "code",
    code_challenge: oauthState.codeChallenge,
    client_id: clientID,
    state: oauthState.state,
    prompt: "login",
  });
  const authorizeUrl = new URL(
    `/authorize?${params.toString()}`,
    "https://auth.augmentcode.com"
  );
  return authorizeUrl.toString();
};

const getAccessToken = async (
  tenant_url: string,
  codeVerifier: string,
  code: string
) => {
  const data = {
    grant_type: "authorization_code",
    client_id: clientID,
    code_verifier: codeVerifier,
    redirect_uri: "",
    code: code,
  };
  const response = await fetch(`${tenant_url}token`, {
    method: "POST",
    body: JSON.stringify(data),
    redirect: "follow",
    headers: {
      "Content-Type": "application/json",
    },
  });
  const json = await response.json();
  const token = json.access_token;
  return token;
};

const parseCode = (code: string) => {
  const parsed = JSON.parse(code);
  return {
    code: parsed.code,
    state: parsed.state,
    tenant_url: parsed.tenant_url,
  };
};

(async () => {
  const oauthState = createOAuthState();
  const url = generateAuthorizeURL(oauthState);
  console.log(url);
  const rl = readline.createInterface({ input, output });
  const code = await rl.question("Enter the code:");
  rl.close();
  console.log(code);
  const parsedCode = parseCode(code);
  console.log(parsedCode);
  const token = await getAccessToken(
    parsedCode.tenant_url,
    oauthState.codeVerifier,
    parsedCode.code
  );
  console.log(token);
  const response = await fetch(`${parsedCode.tenant_url}chat-stream`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({
      chat_history: [
        {
          response_text: "你好 Jason! 我是 Augment，很高兴为你提供帮助。",
          request_message: "你好，我是jason",
        },
      ],
      message: "我叫什么名字",
      mode: "CHAT",
    }),
  });
  const reader = response.body?.getReader();

  if (reader) {
    const decoder = new TextDecoder();
    while (true) {
      const result = await reader.read();
      if (result.done) {
        console.log("done");
        break;
      }
      const text = decoder.decode(result.value, { stream: true });
      try {
        const lines = text.split("\n");
        for (const line of lines) {
          if (!line) continue;
          const json = JSON.parse(line);
          console.log(json);
        }
      } catch (error) {
        console.log("error", text);
      }
    }
  } else {
    console.error("无法获取响应数据流");
  }
})();