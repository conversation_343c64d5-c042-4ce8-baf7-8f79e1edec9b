// ==UserScript==
// @name         AugmentCode Token Helper Simple
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  简化版AugmentCode授权码和Token获取工具
// <AUTHOR>
// @match        https://*.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_registerMenuCommand
// @grant        GM_openInTab
// ==/UserScript==

(function() {
    'use strict';

    // 控制台日志函数
    function log(message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] [AugmentCode] ${message}`, data || '');
    }

    // PKCE相关函数
    function base64URLEncode(buffer) {
        return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    async function sha256Hash(input) {
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return hashBuffer;
    }

    async function createOAuthState() {
        log('开始创建OAuth状态...');
        
        const codeVerifierArray = new Uint8Array(32);
        crypto.getRandomValues(codeVerifierArray);
        const codeVerifier = base64URLEncode(codeVerifierArray.buffer);
        
        const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));
        
        const stateArray = new Uint8Array(8);
        crypto.getRandomValues(stateArray);
        const state = base64URLEncode(stateArray.buffer);

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: Date.now()
        };

        log('OAuth状态创建完成', oauthState);
        GM_setValue('oauthState', JSON.stringify(oauthState));
        return oauthState;
    }

    // 解析授权码
    function parseCode(code) {
        log('开始解析授权码...', { inputCode: code });
        
        try {
            const parsed = JSON.parse(code);
            const result = {
                code: parsed.code,
                state: parsed.state,
                tenant_url: parsed.tenant_url,
            };
            
            log('授权码解析成功', result);
            return result;
        } catch (error) {
            log('授权码解析失败', { error: error.message, inputCode: code });
            throw new Error(`授权码解析失败: ${error.message}`);
        }
    }

    // 获取token函数
    async function getAccessToken(tenant_url, codeVerifier, code) {
        log('开始获取访问令牌...', { tenant_url, codeVerifier, code });
        
        const clientID = "v";
        const data = {
            grant_type: "authorization_code",
            client_id: clientID,
            code_verifier: codeVerifier,
            redirect_uri: "",
            code: code
        };

        log('准备发送token请求', { endpoint: `${tenant_url}token`, data });

        try {
            const result = await new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: "POST",
                    url: `${tenant_url}token`,
                    data: JSON.stringify(data),
                    headers: {
                        "Content-Type": "application/json"
                    },
                    onload: function(response) {
                        log('收到token响应', { 
                            status: response.status, 
                            responseText: response.responseText 
                        });
                        
                        if (response.status === 200) {
                            try {
                                const json = JSON.parse(response.responseText);
                                if (json.access_token) {
                                    log('Token获取成功', { token: json.access_token });
                                    resolve(json.access_token);
                                } else {
                                    log('响应中没有access_token', json);
                                    reject(new Error(`No access_token in response: ${response.responseText}`));
                                }
                            } catch (error) {
                                log('JSON解析错误', { error: error.message });
                                reject(new Error(`JSON parse error: ${error.message}`));
                            }
                        } else {
                            log('HTTP请求失败', { status: response.status, responseText: response.responseText });
                            reject(new Error(`HTTP ${response.status}: ${response.responseText}`));
                        }
                    },
                    onerror: function(error) {
                        log('网络请求错误', error);
                        reject(new Error(`Network error: ${error}`));
                    }
                });
            });

            return result;
        } catch (error) {
            log('Token获取失败', { error: error.message });
            throw error;
        }
    }

    // 生成授权链接
    async function generateAuthorizationUrl() {
        log('开始生成授权链接...');
        
        try {
            const oauthState = await createOAuthState();
            const baseUrl = 'https://auth.augmentcode.com/authorize';
            const params = new URLSearchParams({
                client_id: 'v',
                response_type: 'code',
                redirect_uri: '',
                code_challenge: oauthState.codeChallenge,
                code_challenge_method: 'S256',
                state: oauthState.state
            });

            const authUrl = `${baseUrl}?${params.toString()}`;
            log('授权链接生成完成', { authUrl });
            return authUrl;
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            throw error;
        }
    }

    // 处理菜单命令：生成授权链接
    async function handleGenerateAuthLink() {
        try {
            const authUrl = await generateAuthorizationUrl();
            GM_openInTab(authUrl, true);
            log('授权链接已在新标签页打开');
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            alert(`生成授权链接失败: ${error.message}`);
        }
    }

    // 直接从script标签提取授权码
    function extractFromScript() {
        log('=== 直接从Script标签提取授权码 ===');

        const scripts = document.querySelectorAll('script');
        log('找到script标签数量', scripts.length);

        for (let i = 0; i < scripts.length; i++) {
            const script = scripts[i];
            if (script.textContent) {
                const content = script.textContent;

                // 如果script包含code关键词，就详细分析
                if (content.includes('code')) {
                    log(`=== 分析Script ${i} ===`);
                    log('Script内容长度', content.length);
                    log('Script完整内容', content);

                    // 查找所有可能的JSON对象
                    const jsonPattern = /\{[^{}]*\}/g;
                    const matches = content.match(jsonPattern);

                    if (matches) {
                        log(`在Script ${i}中找到${matches.length}个JSON对象`);

                        for (let j = 0; j < matches.length; j++) {
                            const jsonStr = matches[j];
                            log(`JSON对象 ${j}:`, jsonStr);

                            // 如果包含code就尝试解析
                            if (jsonStr.includes('code')) {
                                log(`JSON对象 ${j} 包含code，尝试解析...`);

                                try {
                                    const parsed = JSON.parse(jsonStr);
                                    log(`JSON对象 ${j} 解析成功:`, parsed);

                                    // 直接返回，不管其他字段
                                    if (parsed.code) {
                                        log('找到code字段，构造返回值...');

                                        const result = {
                                            code: parsed.code,
                                            state: parsed.state || 'extracted_from_script',
                                            tenant_url: parsed.tenant_url || window.location.origin + '/'
                                        };

                                        const resultJson = JSON.stringify(result);
                                        log('成功提取授权码:', resultJson);
                                        return resultJson;
                                    }
                                } catch (e) {
                                    log(`JSON对象 ${j} 解析失败:`, e.message);
                                }
                            }
                        }
                    }

                    // 如果JSON解析失败，尝试正则提取
                    const codeMatch = content.match(/"code"\s*:\s*"([^"]+)"/);
                    if (codeMatch) {
                        log('通过正则找到code值:', codeMatch[1]);

                        const stateMatch = content.match(/"state"\s*:\s*"([^"]+)"/);
                        const tenantMatch = content.match(/"tenant_url"\s*:\s*"([^"]+)"/);

                        const result = {
                            code: codeMatch[1],
                            state: stateMatch ? stateMatch[1] : 'extracted_from_script',
                            tenant_url: tenantMatch ? tenantMatch[1] : window.location.origin + '/'
                        };

                        const resultJson = JSON.stringify(result);
                        log('通过正则成功提取授权码:', resultJson);
                        return resultJson;
                    }
                }
            }
        }

        log('Script标签中未找到授权码');
        return null;
    }

    // 增强的授权码检测
    function simpleExtractCode() {
        log('开始增强授权码检测...');
        log('当前页面URL', window.location.href);
        log('当前页面标题', document.title);

        // 首先尝试直接从script提取
        const scriptResult = extractFromScript();
        if (scriptResult) {
            return scriptResult;
        }

        // 方法1: 从URL参数中提取
        if (window.location.search.includes('code=')) {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const tenant_url = window.location.origin + '/';

            const authCode = JSON.stringify({
                code: code,
                state: state,
                tenant_url: tenant_url
            });

            log('从URL参数提取到授权码', { code, state, tenant_url });
            return authCode;
        }

        // 方法2: 从页面文本中搜索JSON模式
        try {
            const pageText = document.body.textContent || document.body.innerText;
            log('页面文本长度', pageText.length);

            const jsonMatches = pageText.match(/\{"code":"[^"]+","state":"[^"]+","tenant_url":"[^"]+"\}/g);
            if (jsonMatches && jsonMatches.length > 0) {
                const jsonText = jsonMatches[0];
                log('从页面文本中找到JSON模式', { jsonText });

                const parsed = JSON.parse(jsonText);
                if (parsed.code && parsed.state && parsed.tenant_url) {
                    log('成功解析页面中的授权码JSON', parsed);
                    return jsonText;
                }
            }
        } catch (e) {
            log('页面文本检测失败', { error: e.message });
        }

        // 方法3: 检查所有script标签中的内容
        try {
            const scripts = document.querySelectorAll('script');
            log('找到script标签数量', scripts.length);

            for (let i = 0; i < scripts.length; i++) {
                const script = scripts[i];
                if (script.textContent) {
                    const content = script.textContent;
                    log(`检查Script ${i}`, {
                        length: content.length,
                        hasCode: content.includes('code'),
                        hasState: content.includes('state'),
                        hasTenantUrl: content.includes('tenant_url'),
                        preview: content.substring(0, 100)
                    });

                    // 更宽松的搜索条件 - 只要包含code就检查
                    if (content.includes('code')) {
                        log(`Script ${i} 包含code关键词，详细内容:`, { content: content.substring(0, 500) });

                        // 多种JSON格式匹配
                        const patterns = [
                            // 标准格式
                            /\{"code":"[^"]+","state":"[^"]+","tenant_url":"[^"]+"\}/g,
                            // 不同顺序
                            /\{"state":"[^"]+","code":"[^"]+","tenant_url":"[^"]+"\}/g,
                            /\{"tenant_url":"[^"]+","code":"[^"]+","state":"[^"]+"\}/g,
                            // 带空格的格式
                            /\{\s*"code"\s*:\s*"[^"]+"\s*,\s*"state"\s*:\s*"[^"]+"\s*,\s*"tenant_url"\s*:\s*"[^"]+"\s*\}/g,
                            // 单引号格式
                            /\{'code':'[^']+','state':'[^']+','tenant_url':'[^']+'\}/g,
                            // 更宽松的匹配
                            /"code"\s*:\s*"[^"]+"/g
                        ];

                        for (const pattern of patterns) {
                            const matches = content.match(pattern);
                            if (matches && matches.length > 0) {
                                log(`使用模式 ${pattern} 找到匹配`, matches);

                                for (const match of matches) {
                                    try {
                                        // 如果只匹配到code部分，尝试扩展匹配
                                        if (match.includes('"code"') && !match.includes('"state"')) {
                                            // 在匹配位置附近查找完整的JSON
                                            const index = content.indexOf(match);
                                            const before = content.substring(Math.max(0, index - 200), index);
                                            const after = content.substring(index + match.length, index + match.length + 200);

                                            log('尝试扩展匹配', { before, match, after });

                                            // 查找包含完整信息的JSON
                                            const expandedMatch = content.substring(index - 50, index + match.length + 200);
                                            const fullJsonMatch = expandedMatch.match(/\{[^}]*"code"[^}]*"state"[^}]*"tenant_url"[^}]*\}/);
                                            if (fullJsonMatch) {
                                                const jsonText = fullJsonMatch[0];
                                                log('找到扩展的JSON', { jsonText });

                                                const parsed = JSON.parse(jsonText);
                                                if (parsed.code && parsed.state && parsed.tenant_url) {
                                                    log('成功解析扩展的授权码JSON', parsed);
                                                    return jsonText;
                                                }
                                            }
                                        } else {
                                            // 直接尝试解析
                                            const parsed = JSON.parse(match);
                                            if (parsed.code && parsed.state && parsed.tenant_url) {
                                                log('成功解析script中的授权码JSON', parsed);
                                                return match;
                                            }
                                        }
                                    } catch (e) {
                                        log('JSON解析失败', { match, error: e.message });
                                    }
                                }
                            }
                        }

                        // 如果正则匹配失败，尝试手动查找
                        if (content.includes('"code"') && content.includes('"state"') && content.includes('"tenant_url"')) {
                            log('尝试手动提取JSON...');

                            // 查找所有可能的JSON对象
                            const jsonObjects = content.match(/\{[^{}]*\}/g);
                            if (jsonObjects) {
                                log('找到JSON对象', jsonObjects.length);

                                for (const obj of jsonObjects) {
                                    if (obj.includes('"code"') && obj.includes('"state"') && obj.includes('"tenant_url"')) {
                                        log('找到包含所有字段的JSON对象', { obj });

                                        try {
                                            const parsed = JSON.parse(obj);
                                            if (parsed.code && parsed.state && parsed.tenant_url) {
                                                log('成功解析手动找到的授权码JSON', parsed);
                                                return obj;
                                            }
                                        } catch (e) {
                                            log('手动JSON解析失败', { obj, error: e.message });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (e) {
            log('Script标签检测失败', { error: e.message });
        }

        // 方法4: 检查所有data属性
        try {
            const allElements = document.querySelectorAll('*[data-*]');
            log('找到带data属性的元素数量', allElements.length);

            for (const element of allElements) {
                for (const attr of element.attributes) {
                    if (attr.name.startsWith('data-') && attr.value) {
                        if (attr.value.includes('code') && attr.value.includes('state')) {
                            log('找到可能的data属性', { name: attr.name, value: attr.value });

                            try {
                                const parsed = JSON.parse(attr.value);
                                if (parsed.code && parsed.state && parsed.tenant_url) {
                                    log('成功解析data属性中的授权码JSON', parsed);
                                    return attr.value;
                                }
                            } catch (e) {
                                // 不是JSON，继续
                            }
                        }
                    }
                }
            }
        } catch (e) {
            log('Data属性检测失败', { error: e.message });
        }

        // 方法5: 检查window对象中的变量
        try {
            log('检查window对象中的变量...');

            // 检查常见的变量名
            const possibleVars = ['authCode', 'code', 'authData', 'loginData', 'userData', 'config', 'data'];
            for (const varName of possibleVars) {
                if (window[varName]) {
                    log(`找到window.${varName}`, window[varName]);

                    try {
                        let data = window[varName];
                        if (typeof data === 'string') {
                            data = JSON.parse(data);
                        }

                        if (data && data.code && data.state && data.tenant_url) {
                            log(`成功从window.${varName}解析授权码`, data);
                            return JSON.stringify(data);
                        }
                    } catch (e) {
                        // 不是有效的授权码数据
                    }
                }
            }
        } catch (e) {
            log('Window变量检测失败', { error: e.message });
        }

        // 方法6: 检查所有隐藏元素
        try {
            const hiddenElements = document.querySelectorAll('input[type="hidden"], [style*="display:none"], [style*="display: none"]');
            log('找到隐藏元素数量', hiddenElements.length);

            for (const element of hiddenElements) {
                const value = element.value || element.textContent || element.innerHTML;
                if (value && value.includes('code') && value.includes('state')) {
                    log('找到可能的隐藏元素', { tagName: element.tagName, value });

                    try {
                        const parsed = JSON.parse(value);
                        if (parsed.code && parsed.state && parsed.tenant_url) {
                            log('成功解析隐藏元素中的授权码JSON', parsed);
                            return value;
                        }
                    } catch (e) {
                        // 不是JSON，继续
                    }
                }
            }
        } catch (e) {
            log('隐藏元素检测失败', { error: e.message });
        }

        // 方法7: 输出页面完整内容用于调试并强制解析
        try {
            log('=== 调试信息 ===');
            log('页面HTML长度', document.documentElement.outerHTML.length);

            // 查找所有包含"code"的文本
            const allText = document.documentElement.outerHTML;
            const codeMatches = allText.match(/"code"[^,}]*/g);
            if (codeMatches) {
                log('找到所有包含"code"的文本片段', codeMatches);
            }

            // 查找所有可能的JSON结构
            const possibleJsons = allText.match(/\{[^{}]*"[^"]*"[^{}]*\}/g);
            if (possibleJsons) {
                log('找到可能的JSON结构数量', possibleJsons.length);

                for (let index = 0; index < possibleJsons.length; index++) {
                    const json = possibleJsons[index];
                    if (json.includes('code') || json.includes('state') || json.includes('tenant')) {
                        log(`可能的JSON ${index}`, json);

                        // 立即尝试解析这个JSON
                        try {
                            const parsed = JSON.parse(json);
                            log(`JSON ${index} 解析成功`, parsed);

                            // 检查是否包含授权码相关字段（更宽松的条件）
                            if (parsed.code || parsed.authorization_code || parsed.auth_code) {
                                log(`JSON ${index} 包含授权码字段，尝试构造标准格式`);

                                // 尝试构造标准格式
                                const standardFormat = {
                                    code: parsed.code || parsed.authorization_code || parsed.auth_code,
                                    state: parsed.state || parsed.csrf_token || parsed.session_state,
                                    tenant_url: parsed.tenant_url || parsed.tenantUrl || parsed.base_url || parsed.api_url || window.location.origin + '/'
                                };

                                log(`构造的标准格式`, standardFormat);

                                // 如果至少有code字段，就认为找到了
                                if (standardFormat.code) {
                                    // 补充缺失的字段
                                    if (!standardFormat.state) {
                                        standardFormat.state = 'unknown_state';
                                        log('state字段缺失，使用默认值');
                                    }
                                    if (!standardFormat.tenant_url) {
                                        standardFormat.tenant_url = window.location.origin + '/';
                                        log('tenant_url字段缺失，使用当前域名');
                                    }

                                    const result = JSON.stringify(standardFormat);
                                    log('成功构造授权码JSON', result);
                                    return result;
                                }
                            }

                            // 检查是否是完整的标准格式
                            if (parsed.code && parsed.state && parsed.tenant_url) {
                                log(`JSON ${index} 是完整的标准格式授权码`);
                                return json;
                            }

                        } catch (parseError) {
                            log(`JSON ${index} 解析失败`, { json, error: parseError.message });

                            // 尝试修复常见的JSON格式问题
                            let fixedJson = json;

                            // 修复单引号问题
                            if (json.includes("'")) {
                                fixedJson = json.replace(/'/g, '"');
                                log(`尝试修复单引号: ${fixedJson}`);

                                try {
                                    const parsed = JSON.parse(fixedJson);
                                    log(`修复后解析成功`, parsed);

                                    if (parsed.code || parsed.authorization_code) {
                                        const standardFormat = {
                                            code: parsed.code || parsed.authorization_code,
                                            state: parsed.state || 'unknown_state',
                                            tenant_url: parsed.tenant_url || window.location.origin + '/'
                                        };

                                        const result = JSON.stringify(standardFormat);
                                        log('修复后成功构造授权码JSON', result);
                                        return result;
                                    }
                                } catch (e) {
                                    log('修复后仍然解析失败', e.message);
                                }
                            }
                        }
                    }
                }
            }
        } catch (e) {
            log('调试信息输出失败', { error: e.message });
        }

        log('所有检测方法都未找到授权码');
        return null;
    }

    // 自动获取Token流程
    async function autoGetToken(authCode) {
        log('开始自动Token获取流程...', { authCode });

        try {
            const parsedCode = parseCode(authCode);
            const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));

            if (!savedOAuthState.codeVerifier) {
                throw new Error('未找到保存的Code Verifier，请先生成授权链接');
            }

            log('找到保存的OAuth状态', savedOAuthState);

            if (savedOAuthState.state !== parsedCode.state) {
                throw new Error('State不匹配，可能存在安全风险');
            }

            log('State验证通过');

            const token = await getAccessToken(
                parsedCode.tenant_url,
                savedOAuthState.codeVerifier,
                parsedCode.code
            );

            log('Token获取流程完成', { token });
            GM_deleteValue('oauthState');
            log('已清除保存的OAuth状态');

            return { token, parsedCode, success: true };

        } catch (error) {
            log('Token获取流程失败', { error: error.message });
            return { error: error.message, success: false };
        }
    }

    // 创建简化UI
    function createSimpleUI() {
        log('开始创建简化UI...');

        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '10px';
        container.style.right = '10px';
        container.style.padding = '15px';
        container.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        container.style.border = '2px solid #007bff';
        container.style.borderRadius = '8px';
        container.style.zIndex = '10000';
        container.style.width = '350px';
        container.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
        container.style.fontFamily = 'Arial, sans-serif';

        const title = document.createElement('h3');
        title.textContent = 'AugmentCode Token Helper';
        title.style.margin = '0 0 15px 0';
        title.style.color = '#007bff';
        title.style.borderBottom = '2px solid #007bff';
        title.style.paddingBottom = '5px';
        container.appendChild(title);

        // OAuth状态显示
        const statusSection = document.createElement('div');
        statusSection.style.margin = '15px 0';
        statusSection.style.padding = '10px';
        statusSection.style.backgroundColor = '#e9ecef';
        statusSection.style.border = '1px solid #ced4da';
        statusSection.style.borderRadius = '4px';
        statusSection.style.fontSize = '12px';

        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
        if (savedOAuthState.codeVerifier) {
            statusSection.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#28a745;">已准备</span><br>
                <strong>State:</strong> ${savedOAuthState.state}<br>
                <strong>创建时间:</strong> ${new Date(savedOAuthState.creationTime).toLocaleString()}
            `;
        } else {
            statusSection.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#dc3545;">未准备</span><br>
                <small>请先通过菜单"生成授权链接"创建OAuth状态</small>
            `;
        }
        container.appendChild(statusSection);

        // 检测区域
        const detectSection = document.createElement('div');
        detectSection.style.margin = '15px 0';
        detectSection.style.padding = '10px';
        detectSection.style.backgroundColor = '#d4edda';
        detectSection.style.border = '1px solid #c3e6cb';
        detectSection.style.borderRadius = '4px';

        const detectTitle = document.createElement('h4');
        detectTitle.textContent = '授权码检测';
        detectTitle.style.margin = '0 0 10px 0';
        detectTitle.style.color = '#155724';
        detectSection.appendChild(detectTitle);

        const detectResult = document.createElement('div');
        detectResult.style.margin = '10px 0';
        detectResult.style.wordBreak = 'break-all';
        detectResult.style.fontSize = '12px';
        detectSection.appendChild(detectResult);

        // 检测按钮
        const detectBtn = document.createElement('button');
        detectBtn.textContent = '检测授权码';
        detectBtn.style.margin = '5px 5px 5px 0';
        detectBtn.style.padding = '8px 15px';
        detectBtn.style.backgroundColor = '#17a2b8';
        detectBtn.style.color = 'white';
        detectBtn.style.border = 'none';
        detectBtn.style.borderRadius = '4px';
        detectBtn.style.cursor = 'pointer';
        detectSection.appendChild(detectBtn);

        // 剪贴板按钮
        const clipboardBtn = document.createElement('button');
        clipboardBtn.textContent = '从剪贴板获取';
        clipboardBtn.style.margin = '5px';
        clipboardBtn.style.padding = '8px 15px';
        clipboardBtn.style.backgroundColor = '#ffc107';
        clipboardBtn.style.color = '#212529';
        clipboardBtn.style.border = 'none';
        clipboardBtn.style.borderRadius = '4px';
        clipboardBtn.style.cursor = 'pointer';
        detectSection.appendChild(clipboardBtn);

        container.appendChild(detectSection);

        // 检测按钮事件
        detectBtn.addEventListener('click', () => {
            log('手动触发授权码检测');
            detectResult.innerHTML = '<span style="color:blue;">正在检测...</span>';

            setTimeout(() => {
                const authCode = simpleExtractCode();
                if (authCode) {
                    try {
                        const parsed = parseCode(authCode);
                        detectResult.innerHTML = `
                            <strong>检测到授权码:</strong><br>
                            <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                            <strong>State:</strong> ${parsed.state}<br>
                            <strong>Tenant URL:</strong> ${parsed.tenant_url}<br>
                            <button id="getTokenBtn" style="margin-top:10px; padding:8px 15px; background:#28a745; color:white; border:none; border-radius:4px; cursor:pointer;">获取Token</button>
                            <div id="tokenResult" style="margin-top:10px;"></div>
                        `;

                        document.getElementById('getTokenBtn').addEventListener('click', async () => {
                            const btn = document.getElementById('getTokenBtn');
                            const result = document.getElementById('tokenResult');

                            btn.disabled = true;
                            btn.textContent = '获取中...';

                            const tokenResult = await autoGetToken(authCode);

                            if (tokenResult.success) {
                                result.innerHTML = `
                                    <div style="background:#d4edda; padding:10px; border:1px solid #c3e6cb; border-radius:4px;">
                                        <strong>Token获取成功!</strong><br>
                                        <textarea readonly style="width:100%;height:80px;font-size:10px; margin:5px 0;">${tokenResult.token}</textarea><br>
                                        <button onclick="navigator.clipboard.writeText('${tokenResult.token}');alert('已复制Token!')">复制Token</button>
                                    </div>
                                `;
                            } else {
                                result.innerHTML = `
                                    <div style="background:#f8d7da; padding:10px; border:1px solid #f5c6cb; border-radius:4px;">
                                        <strong>Token获取失败:</strong><br>
                                        <span style="color:red">${tokenResult.error}</span>
                                    </div>
                                `;
                            }

                            btn.disabled = false;
                            btn.textContent = '获取Token';
                        });

                    } catch (error) {
                        detectResult.innerHTML = `<span style="color:red">授权码解析失败: ${error.message}</span>`;
                    }
                } else {
                    detectResult.innerHTML = '<span style="color:#856404;">未检测到授权码</span>';
                }
            }, 500);
        });

        // 剪贴板按钮事件
        clipboardBtn.addEventListener('click', async () => {
            try {
                const clipboardText = await navigator.clipboard.readText();
                log('从剪贴板读取内容', { clipboardText });

                const parsed = JSON.parse(clipboardText);
                if (parsed.code && parsed.state && parsed.tenant_url) {
                    log('剪贴板中找到有效授权码', parsed);

                    detectResult.innerHTML = `
                        <strong>从剪贴板获取到授权码:</strong><br>
                        <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                        <strong>State:</strong> ${parsed.state}<br>
                        <strong>Tenant URL:</strong> ${parsed.tenant_url}<br>
                        <button id="getTokenBtn2" style="margin-top:10px; padding:8px 15px; background:#28a745; color:white; border:none; border-radius:4px; cursor:pointer;">获取Token</button>
                        <div id="tokenResult2" style="margin-top:10px;"></div>
                    `;

                    document.getElementById('getTokenBtn2').addEventListener('click', async () => {
                        const btn = document.getElementById('getTokenBtn2');
                        const result = document.getElementById('tokenResult2');

                        btn.disabled = true;
                        btn.textContent = '获取中...';

                        const tokenResult = await autoGetToken(clipboardText);

                        if (tokenResult.success) {
                            result.innerHTML = `
                                <div style="background:#d4edda; padding:10px; border:1px solid #c3e6cb; border-radius:4px;">
                                    <strong>Token获取成功!</strong><br>
                                    <textarea readonly style="width:100%;height:80px;font-size:10px; margin:5px 0;">${tokenResult.token}</textarea><br>
                                    <button onclick="navigator.clipboard.writeText('${tokenResult.token}');alert('已复制Token!')">复制Token</button>
                                </div>
                            `;
                        } else {
                            result.innerHTML = `
                                <div style="background:#f8d7da; padding:10px; border:1px solid #f5c6cb; border-radius:4px;">
                                    <strong>Token获取失败:</strong><br>
                                    <span style="color:red">${tokenResult.error}</span>
                                </div>
                            `;
                        }

                        btn.disabled = false;
                        btn.textContent = '获取Token';
                    });

                } else {
                    detectResult.innerHTML = '<span style="color:red">剪贴板内容不是有效的授权码JSON</span>';
                }
            } catch (error) {
                log('从剪贴板获取失败', { error: error.message });
                detectResult.innerHTML = `<span style="color:red">从剪贴板获取失败: ${error.message}</span>`;
            }
        });

        log('简化UI创建完成');
        return container;
    }

    // 注册菜单命令
    GM_registerMenuCommand('生成授权链接', handleGenerateAuthLink);

    // 页面加载完成后创建UI
    window.addEventListener('load', () => {
        log('页面加载完成，开始初始化');
        try {
            const ui = createSimpleUI();
            document.body.appendChild(ui);
            log('简化版AugmentCode Token Helper已启动');
        } catch (error) {
            log('UI创建失败', { error: error.message });
            console.error('UI创建失败:', error);
        }
    });

})();
