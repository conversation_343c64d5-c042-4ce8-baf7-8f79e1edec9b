"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const crypto_1 = require("crypto");
const readline = __importStar(require("node:readline/promises"));
const node_process_1 = require("node:process");
const dotenv = __importStar(require("dotenv"));
// 尝试加载环境变量
try {
    dotenv.config();
}
catch (error) {
    console.warn("警告: 无法加载 .env 文件，使用默认配置");
}
// 配置参数
const CONFIG = {
    clientID: process.env.CLIENT_ID || "your-client-id",
    redirectUri: process.env.REDIRECT_URI || "http://localhost:3000/callback",
    authEndpoint: "https://auth.augmentcode.com",
    apiTimeout: 30000, // 30秒超时
};
/**
 * 将 Buffer 编码为 base64url 格式
 * @param buffer 输入 Buffer
 * @returns base64url 编码的字符串
 */
function base64URLEncode(buffer) {
    return buffer
        .toString("base64")
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=/g, "");
}
/**
 * 计算输入的 SHA-256 哈希值
 * @param input 输入字符串或 Buffer
 * @returns 哈希值 Buffer
 */
function sha256Hash(input) {
    return (0, crypto_1.createHash)("sha256").update(input).digest();
}
/**
 * 创建 OAuth 状态对象，包含 PKCE 挑战相关参数
 * @returns OAuth 状态对象
 */
function createOAuthState() {
    const codeVerifier = base64URLEncode((0, crypto_1.randomBytes)(32));
    const codeChallenge = base64URLEncode(sha256Hash(Buffer.from(codeVerifier)));
    const state = base64URLEncode((0, crypto_1.randomBytes)(8));
    const oauthState = {
        codeVerifier,
        codeChallenge,
        state,
        creationTime: Date.now(),
    };
    console.log("OAuth 状态创建成功");
    return oauthState;
}
/**
 * 生成授权 URL
 * @param oauthState OAuth 状态对象
 * @returns 完整的授权 URL
 */
const generateAuthorizeURL = (oauthState) => {
    const params = new URLSearchParams({
        response_type: "code",
        code_challenge: oauthState.codeChallenge,
        client_id: CONFIG.clientID,
        state: oauthState.state,
        prompt: "login",
        redirect_uri: CONFIG.redirectUri,
    });
    const authorizeUrl = new URL(`/authorize?${params.toString()}`, CONFIG.authEndpoint);
    return authorizeUrl.toString();
};
/**
 * 获取访问令牌
 * @param tenant_url 租户 URL
 * @param codeVerifier 代码验证器
 * @param code 授权码
 * @returns 访问令牌
 */
const getAccessToken = (tenant_url, codeVerifier, code) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const data = {
            grant_type: "authorization_code",
            client_id: CONFIG.clientID,
            code_verifier: codeVerifier,
            redirect_uri: CONFIG.redirectUri,
            code: code,
        };
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), CONFIG.apiTimeout);
        const response = yield fetch(`${tenant_url}token`, {
            method: "POST",
            body: JSON.stringify(data),
            redirect: "follow",
            headers: {
                "Content-Type": "application/json",
            },
            signal: controller.signal,
        });
        clearTimeout(timeoutId);
        if (!response.ok) {
            throw new Error(`获取令牌失败: ${response.status} ${response.statusText}`);
        }
        const json = yield response.json();
        const token = json.access_token;
        if (!token) {
            throw new Error("响应中没有访问令牌");
        }
        return token;
    }
    catch (error) {
        console.error("获取访问令牌时出错:", error);
        throw error;
    }
});
/**
 * 解析授权码 JSON
 * @param code 授权码 JSON 字符串
 * @returns 解析后的授权码对象
 */
const parseCode = (code) => {
    try {
        const parsed = JSON.parse(code);
        return {
            code: parsed.code,
            state: parsed.state,
            tenant_url: parsed.tenant_url,
        };
    }
    catch (error) {
        console.error("解析授权码时出错:", error);
        throw new Error("授权码格式无效，请确保输入正确的 JSON 格式");
    }
};
/**
 * 与 Augment 聊天 API 交互
 * @param tenant_url 租户 URL
 * @param token 访问令牌
 * @param message 消息内容
 */
function chatWithAugment(tenant_url, token, message) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        try {
            const response = yield fetch(`${tenant_url}chat-stream`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    chat_history: [
                        {
                            response_text: "你好 Jason! 我是 Augment，很高兴为你提供帮助。",
                            request_message: "你好，我是jason",
                        },
                    ],
                    message: message,
                    mode: "CHAT",
                }),
            });
            if (!response.ok) {
                throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
            }
            const reader = (_a = response.body) === null || _a === void 0 ? void 0 : _a.getReader();
            if (reader) {
                const decoder = new TextDecoder();
                while (true) {
                    const result = yield reader.read();
                    if (result.done) {
                        console.log("流读取完成");
                        break;
                    }
                    const text = decoder.decode(result.value, { stream: true });
                    try {
                        const lines = text.split("\n");
                        for (const line of lines) {
                            if (!line)
                                continue;
                            const json = JSON.parse(line);
                            console.log(json);
                        }
                    }
                    catch (error) {
                        console.error("解析响应数据时出错:", error);
                        console.log("原始文本:", text);
                    }
                }
            }
            else {
                console.error("无法获取响应数据流");
                throw new Error("服务器未返回数据流");
            }
        }
        catch (error) {
            console.error("与 Augment 聊天时出错:", error);
            throw error;
        }
    });
}
/**
 * 主函数
 */
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log("正在初始化 Augment API 客户端...");
            // 检查配置
            if (CONFIG.clientID === "your-client-id") {
                console.warn("警告: 使用默认客户端 ID。请在 .env 文件中设置 CLIENT_ID");
            }
            // 创建 OAuth 状态
            const oauthState = createOAuthState();
            const url = generateAuthorizeURL(oauthState);
            console.log("请在浏览器中打开以下 URL 进行授权:");
            console.log(url);
            // 获取授权码
            const rl = readline.createInterface({ input: node_process_1.stdin, output: node_process_1.stdout });
            const code = yield rl.question("请输入授权码:");
            rl.close();
            // 解析授权码
            console.log("正在解析授权码...");
            const parsedCode = parseCode(code);
            console.log("授权码解析成功");
            // 验证状态
            if (parsedCode.state !== oauthState.state) {
                throw new Error("状态不匹配，可能存在安全风险");
            }
            // 获取访问令牌
            console.log("正在获取访问令牌...");
            const token = yield getAccessToken(parsedCode.tenant_url, oauthState.codeVerifier, parsedCode.code);
            console.log("访问令牌获取成功");
            // 发送消息
            const chatRl = readline.createInterface({ input: node_process_1.stdin, output: node_process_1.stdout });
            const message = yield chatRl.question("请输入要发送给 Augment 的消息:");
            chatRl.close();
            console.log("正在与 Augment 聊天...");
            yield chatWithAugment(parsedCode.tenant_url, token, message);
            console.log("会话结束");
        }
        catch (error) {
            console.error("程序执行出错:", error);
            process.exit(1);
        }
    });
}
// 启动程序
main();
//# sourceMappingURL=augment2api.js.map