# Augment2API 使用指南

## 准备工作

1. 确保已安装 Node.js (14.x 或更高版本)
2. 复制 `.env.example` 文件为 `.env`：
   ```bash
   cp .env.example .env
   ```
3. 编辑 `.env` 文件，填入您的 OAuth 客户端 ID 和重定向 URI

## 安装

```bash
# 安装依赖
npm install
```

## 运行

```bash
# 开发模式
npm run dev

# 或者直接启动
npm start
```

## 使用流程

1. 运行程序后，终端会显示一个授权 URL
2. 在浏览器中打开该 URL
3. 完成授权后，您将被重定向到您配置的重定向 URI，并获得一个 JSON 格式的授权码
4. 将完整的 JSON 授权码复制到终端中
5. 程序将获取访问令牌
6. 输入您想发送给 Augment 的消息
7. 程序将显示 Augment 的响应

## 常见问题

### 如何获取客户端 ID？

请联系 Augment 管理员获取有效的客户端 ID。

### 授权失败怎么办？

- 检查您的客户端 ID 是否正确
- 确认重定向 URI 是否与 Augment 管理员配置的一致
- 检查网络连接是否正常

### 如何退出程序？

程序完成聊天流程后会自动退出。如需中途退出，请按 `Ctrl+C`。

## 代码示例

以下是一个典型的授权码 JSON 格式：

```json
{
  "code": "your_authorization_code",
  "state": "your_state_value",
  "tenant_url": "https://api.example.com/"
}
```

请确保复制完整的 JSON，包括花括号。 