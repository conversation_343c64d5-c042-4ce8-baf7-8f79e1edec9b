// ==UserScript==
// @name         AugmentCode Token Helper Auto
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  自动化AugmentCode授权码和Token获取工具
// <AUTHOR>
// @match        https://*.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_registerMenuCommand
// @grant        GM_openInTab
// ==/UserScript==

(function() {
    'use strict';

    // 控制台日志函数
    function log(message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] [AugmentCode] ${message}`, data || '');
    }

    // Code Verifier展开/收起切换函数
    function toggleVerifier(element, fullVerifier) {
        const isExpanded = element.textContent === fullVerifier;
        if (isExpanded) {
            const shortVerifier = `${fullVerifier.substring(0, 8)}...${fullVerifier.substring(fullVerifier.length - 8)}`;
            element.textContent = shortVerifier;
            element.title = '点击展开完整Code Verifier';
        } else {
            element.textContent = fullVerifier;
            element.title = '点击收起Code Verifier';
        }
    }

    // 将函数暴露到全局作用域
    window.toggleVerifier = toggleVerifier;

    // 统一的自动Token获取函数
    async function autoTriggerTokenGet(authCode, resultContainer, sourceType = '自动检测') {
        log(`开始${sourceType}的自动Token获取流程...`);

        // 创建Token结果显示区域
        const tokenResult = document.createElement('div');
        tokenResult.style.margin = '10px 0';
        tokenResult.style.wordBreak = 'break-all';
        resultContainer.appendChild(tokenResult);

        // 显示自动获取进度
        tokenResult.innerHTML = `
            <div style="background:#fff3cd; padding:10px; border:1px solid #ffeaa7; border-radius:4px; margin-top:10px;">
                <strong>🔄 自动获取Token中...</strong><br>
                <small>正在使用检测到的授权码自动获取访问令牌</small>
            </div>
        `;

        try {
            const result = await autoGetToken(authCode);

            if (result.success) {
                tokenResult.innerHTML = `
                    <div style="background:#d4edda; padding:10px; border:1px solid #c3e6cb; border-radius:4px; margin-top:10px;">
                        <strong>✅ Token获取成功!</strong><br>
                        <textarea readonly style="width:100%;height:80px;font-size:10px; margin:5px 0;">${result.token}</textarea><br>
                        <button onclick="navigator.clipboard.writeText('${result.token}');alert('已复制Token!')" style="padding:5px 10px; background:#28a745; color:white; border:none; border-radius:3px; cursor:pointer;">复制Token</button>
                        <button onclick="this.parentElement.parentElement.style.display='none'" style="padding:5px 10px; background:#6c757d; color:white; border:none; border-radius:3px; cursor:pointer; margin-left:5px;">收起</button>
                    </div>
                `;
                log(`${sourceType}自动Token获取成功`);
            } else {
                tokenResult.innerHTML = `
                    <div style="background:#f8d7da; padding:10px; border:1px solid #f5c6cb; border-radius:4px; margin-top:10px;">
                        <strong>❌ Token获取失败:</strong><br>
                        <span style="color:red">${result.error}</span><br>
                        <button onclick="autoTriggerTokenGet('${authCode}', this.parentElement.parentElement.parentElement, '${sourceType}重试')" style="padding:5px 10px; background:#dc3545; color:white; border:none; border-radius:3px; cursor:pointer; margin-top:5px;">重试获取</button>
                    </div>
                `;
                log(`${sourceType}自动Token获取失败:`, result.error);
            }
        } catch (error) {
            tokenResult.innerHTML = `
                <div style="background:#f8d7da; padding:10px; border:1px solid #f5c6cb; border-radius:4px; margin-top:10px;">
                    <strong>❌ Token获取异常:</strong><br>
                    <span style="color:red">${error.message}</span><br>
                    <button onclick="autoTriggerTokenGet('${authCode}', this.parentElement.parentElement.parentElement, '${sourceType}重试')" style="padding:5px 10px; background:#dc3545; color:white; border:none; border-radius:3px; cursor:pointer; margin-top:5px;">重试获取</button>
                </div>
            `;
            log(`${sourceType}自动Token获取异常:`, error);
        }
    }

    // 将函数暴露到全局作用域
    window.autoTriggerTokenGet = autoTriggerTokenGet;

    // PKCE相关函数
    function base64URLEncode(buffer) {
        return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    async function sha256Hash(input) {
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return hashBuffer;
    }

    async function createOAuthState() {
        log('开始创建OAuth状态...');
        
        const codeVerifierArray = new Uint8Array(32);
        crypto.getRandomValues(codeVerifierArray);
        const codeVerifier = base64URLEncode(codeVerifierArray.buffer);
        
        const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));
        
        const stateArray = new Uint8Array(8);
        crypto.getRandomValues(stateArray);
        const state = base64URLEncode(stateArray.buffer);

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: Date.now()
        };

        log('OAuth状态创建完成', oauthState);
        GM_setValue('oauthState', JSON.stringify(oauthState));
        return oauthState;
    }

    // 解析授权码
    function parseCode(code) {
        log('开始解析授权码...', { inputCode: code });
        
        try {
            const parsed = JSON.parse(code);
            const result = {
                code: parsed.code,
                state: parsed.state,
                tenant_url: parsed.tenant_url,
            };
            
            log('授权码解析成功', result);
            return result;
        } catch (error) {
            log('授权码解析失败', { error: error.message, inputCode: code });
            throw new Error(`授权码解析失败: ${error.message}`);
        }
    }

    // 获取token函数
    async function getAccessToken(tenant_url, codeVerifier, code) {
        log('开始获取访问令牌...', { tenant_url, codeVerifier, code });
        
        const clientID = "v";
        const data = {
            grant_type: "authorization_code",
            client_id: clientID,
            code_verifier: codeVerifier,
            redirect_uri: "",
            code: code
        };

        log('准备发送token请求', { endpoint: `${tenant_url}token`, data });

        try {
            const result = await new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: "POST",
                    url: `${tenant_url}token`,
                    data: JSON.stringify(data),
                    headers: {
                        "Content-Type": "application/json"
                    },
                    onload: function(response) {
                        log('收到token响应', { 
                            status: response.status, 
                            responseText: response.responseText 
                        });
                        
                        if (response.status === 200) {
                            try {
                                const json = JSON.parse(response.responseText);
                                if (json.access_token) {
                                    log('Token获取成功', { token: json.access_token });
                                    resolve(json.access_token);
                                } else {
                                    log('响应中没有access_token', json);
                                    reject(new Error(`No access_token in response: ${response.responseText}`));
                                }
                            } catch (error) {
                                log('JSON解析错误', { error: error.message });
                                reject(new Error(`JSON parse error: ${error.message}`));
                            }
                        } else {
                            log('HTTP请求失败', { status: response.status, responseText: response.responseText });
                            reject(new Error(`HTTP ${response.status}: ${response.responseText}`));
                        }
                    },
                    onerror: function(error) {
                        log('网络请求错误', error);
                        reject(new Error(`Network error: ${error}`));
                    }
                });
            });

            return result;
        } catch (error) {
            log('Token获取失败', { error: error.message });
            throw error;
        }
    }

    // 生成授权链接
    async function generateAuthorizationUrl() {
        log('开始生成授权链接...');
        
        try {
            const oauthState = await createOAuthState();
            const baseUrl = 'https://auth.augmentcode.com/authorize';
            const params = new URLSearchParams({
                client_id: 'v',
                response_type: 'code',
                redirect_uri: '',
                code_challenge: oauthState.codeChallenge,
                code_challenge_method: 'S256',
                state: oauthState.state
            });

            const authUrl = `${baseUrl}?${params.toString()}`;
            log('授权链接生成完成', { authUrl });
            return authUrl;
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            throw error;
        }
    }

    // 处理菜单命令：生成授权链接
    async function handleGenerateAuthLink() {
        try {
            const authUrl = await generateAuthorizationUrl();
            GM_openInTab(authUrl, true);
            log('授权链接已在新标签页打开');
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            alert(`生成授权链接失败: ${error.message}`);
        }
    }

    // 专注于Script标签的授权码提取
    function autoExtractCode() {
        log('=== 开始Script标签授权码提取 ===');

        // 获取所有script标签
        const scripts = document.querySelectorAll('script');
        log('找到script标签数量:', scripts.length);

        for (let i = 0; i < scripts.length; i++) {
            const script = scripts[i];
            if (script.textContent) {
                const content = script.textContent;
                log(`检查script ${i}...`);

                // 方法1: 查找 data = {...} 模式，支持多行
                const dataMatch = content.match(/data\s*=\s*\{[^}]*code[^}]*\}/s);
                if (dataMatch) {
                    log(`在script ${i}中找到data变量:`, dataMatch[0]);

                    const dataStr = dataMatch[0].replace(/data\s*=\s*/, '');
                    log('提取的data内容:', dataStr);

                    // 尝试转换JavaScript对象格式为JSON格式
                    try {
                        // 将JavaScript对象格式转换为JSON格式
                        let jsonStr = dataStr
                            .replace(/\s+/g, ' ')         // 压缩空白字符
                            .replace(/(\w+):/g, '"$1":')  // 给属性名加引号
                            .replace(/,\s*}/g, '}')       // 移除末尾逗号
                            .trim();                      // 移除首尾空格

                        log('转换后的JSON:', jsonStr);

                        // 验证是否为有效JSON
                        const parsed = JSON.parse(jsonStr);
                        log('JSON验证成功:', parsed);

                        // 检查必需字段
                        if (parsed.code && parsed.state && parsed.tenant_url) {
                            // 重新序列化为紧凑格式
                            const compactJson = JSON.stringify(parsed);
                            log('紧凑JSON格式:', compactJson);
                            return compactJson;
                        } else {
                            log('缺少必需字段:', { code: !!parsed.code, state: !!parsed.state, tenant_url: !!parsed.tenant_url });
                        }
                    } catch (e) {
                        log('JSON转换失败，尝试手动提取:', e.message);

                        // 手动提取各个字段
                        const codeMatch = dataStr.match(/code:\s*"([^"]+)"/);
                        const stateMatch = dataStr.match(/state:\s*"([^"]+)"/);
                        const tenantMatch = dataStr.match(/tenant_url:\s*"([^"]+)"/);

                        if (codeMatch && stateMatch && tenantMatch) {
                            const manualJson = JSON.stringify({
                                code: codeMatch[1],
                                state: stateMatch[1],
                                tenant_url: tenantMatch[1]
                            });
                            log('手动构造的JSON:', manualJson);
                            return manualJson;
                        } else {
                            log('手动提取失败，缺少字段:', {
                                code: !!codeMatch,
                                state: !!stateMatch,
                                tenant_url: !!tenantMatch
                            });
                        }
                    }
                }

                // 方法2: 直接搜索标准JSON格式
                const jsonMatches = content.match(/\{"code":"[^"]+","state":"[^"]+","tenant_url":"[^"]+"\}/g);
                if (jsonMatches && jsonMatches.length > 0) {
                    const jsonText = jsonMatches[0];
                    log(`在script ${i}中找到标准JSON:`, jsonText);

                    try {
                        const parsed = JSON.parse(jsonText);
                        if (parsed.code && parsed.state && parsed.tenant_url) {
                            log('标准JSON验证成功:', parsed);
                            return jsonText;
                        }
                    } catch (e) {
                        log('标准JSON解析失败:', e.message);
                    }
                }

                // 方法3: 如果包含关键字段，尝试分别提取
                if (content.includes('code') && content.includes('tenant_url')) {
                    log(`检查script ${i}是否包含分散的字段...`);

                    // 直接搜索code值
                    const codeMatch = content.match(/"code"\s*:\s*"([^"]+)"/);
                    if (codeMatch) {
                        const code = codeMatch[1];
                        log('找到code值:', code);

                        // 搜索state值
                        const stateMatch = content.match(/"state"\s*:\s*"([^"]+)"/);
                        const state = stateMatch ? stateMatch[1] : 'default_state';
                        log('找到state值:', state);

                        // 搜索tenant_url值
                        const tenantMatch = content.match(/"tenant_url"\s*:\s*"([^"]+)"/);
                        const tenant_url = tenantMatch ? tenantMatch[1] : window.location.origin + '/';
                        log('找到tenant_url值:', tenant_url);

                        // 构造结果
                        const result = {
                            code: code,
                            state: state,
                            tenant_url: tenant_url
                        };

                        const resultJson = JSON.stringify(result);
                        log('构造的授权码JSON:', resultJson);
                        return resultJson;
                    }
                }
            }
        }

        log('未找到授权码');
        return null;
    }

    // 自动获取Token流程
    async function autoGetToken(authCode) {
        log('开始自动Token获取流程...', { authCode });

        try {
            // 1. 解析授权码
            const parsedCode = parseCode(authCode);

            // 2. 获取保存的OAuth状态
            const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
            if (!savedOAuthState.codeVerifier) {
                throw new Error('未找到保存的Code Verifier，请先生成授权链接');
            }

            log('找到保存的OAuth状态', savedOAuthState);

            // 3. 跳过State验证（自动检测的授权码可能与保存的state不匹配）
            log('跳过State验证（自动检测模式）');

            // 4. 获取token
            const token = await getAccessToken(
                parsedCode.tenant_url,
                savedOAuthState.codeVerifier,
                parsedCode.code
            );

            log('Token获取流程完成', { token });

            // 5. 清除保存的状态
            GM_deleteValue('oauthState');
            log('已清除保存的OAuth状态');

            return {
                token,
                parsedCode,
                success: true
            };

        } catch (error) {
            log('Token获取流程失败', { error: error.message });
            return {
                error: error.message,
                success: false
            };
        }
    }

    // 创建简化UI
    function createSimpleUI() {
        log('开始创建简化UI...');

        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '10px';
        container.style.right = '10px';
        container.style.padding = '15px';
        container.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        container.style.border = '2px solid #007bff';
        container.style.borderRadius = '8px';
        container.style.zIndex = '10000';
        container.style.width = '350px';
        container.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
        container.style.fontFamily = 'Arial, sans-serif';

        const title = document.createElement('h3');
        title.textContent = 'AugmentCode Token Helper Auto';
        title.style.margin = '0 0 15px 0';
        title.style.color = '#007bff';
        title.style.borderBottom = '2px solid #007bff';
        title.style.paddingBottom = '5px';
        container.appendChild(title);

        // 显示当前OAuth状态
        const statusSection = document.createElement('div');
        statusSection.style.margin = '15px 0';
        statusSection.style.padding = '10px';
        statusSection.style.backgroundColor = '#e9ecef';
        statusSection.style.border = '1px solid #ced4da';
        statusSection.style.borderRadius = '4px';
        statusSection.style.fontSize = '12px';

        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
        if (savedOAuthState.codeVerifier) {
            const shortVerifier = `${savedOAuthState.codeVerifier.substring(0, 8)}...${savedOAuthState.codeVerifier.substring(savedOAuthState.codeVerifier.length - 8)}`;
            statusSection.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#28a745;">已准备</span><br>
                <strong>State:</strong> ${savedOAuthState.state}<br>
                <strong>Code Verifier:</strong> <span onclick="toggleVerifier(this, '${savedOAuthState.codeVerifier}')" style="cursor:pointer; color:#007bff; text-decoration:underline;" title="点击展开完整Code Verifier">${shortVerifier}</span><br>
                <strong>创建时间:</strong> ${new Date(savedOAuthState.creationTime).toLocaleString()}<br>
                <button onclick="GM_deleteValue('oauthState');location.reload();" style="margin-top:5px; padding:3px 8px; font-size:11px; background:#dc3545; color:white; border:none;">清除状态</button>
            `;
        } else {
            statusSection.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#dc3545;">未准备</span><br>
                <small>请先通过菜单"生成授权链接"创建OAuth状态</small>
            `;
        }
        container.appendChild(statusSection);

        // 自动检测区域
        const autoSection = document.createElement('div');
        autoSection.style.margin = '15px 0';
        autoSection.style.padding = '10px';
        autoSection.style.backgroundColor = '#d4edda';
        autoSection.style.border = '1px solid #c3e6cb';
        autoSection.style.borderRadius = '4px';

        const autoTitle = document.createElement('h4');
        autoTitle.textContent = '自动检测';
        autoTitle.style.margin = '0 0 10px 0';
        autoTitle.style.color = '#155724';
        autoSection.appendChild(autoTitle);

        const autoResult = document.createElement('div');
        autoResult.style.margin = '10px 0';
        autoResult.style.wordBreak = 'break-all';
        autoResult.style.fontSize = '12px';
        autoSection.appendChild(autoResult);

        // 添加手动检测按钮
        const detectBtn = document.createElement('button');
        detectBtn.textContent = '重新检测授权码';
        detectBtn.style.margin = '5px 0';
        detectBtn.style.padding = '5px 10px';
        detectBtn.style.backgroundColor = '#17a2b8';
        detectBtn.style.color = 'white';
        detectBtn.style.border = 'none';
        detectBtn.style.borderRadius = '4px';
        detectBtn.style.cursor = 'pointer';
        autoSection.appendChild(detectBtn);

        // 检测Copy按钮提示
        const copyButton = document.querySelector('button[onclick*="clipboard"]') ||
                          Array.from(document.querySelectorAll('button')).find(btn =>
                              btn.textContent.toLowerCase().includes('copy') ||
                              btn.textContent.toLowerCase().includes('clipboard')
                          );

        if (copyButton) {
            const copyTip = document.createElement('div');
            copyTip.style.margin = '10px 0';
            copyTip.style.padding = '8px';
            copyTip.style.backgroundColor = '#fff3cd';
            copyTip.style.border = '1px solid #ffeaa7';
            copyTip.style.borderRadius = '4px';
            copyTip.style.fontSize = '12px';
            copyTip.innerHTML = `
                <strong>💡 提示:</strong> 检测到页面有"Copy to Clipboard"按钮<br>
                请先点击页面上的"Copy to Clipboard"按钮，然后点击下方的"从剪贴板获取"按钮
            `;
            autoSection.appendChild(copyTip);

            const clipboardBtn = document.createElement('button');
            clipboardBtn.textContent = '从剪贴板获取授权码';
            clipboardBtn.style.margin = '5px 0';
            clipboardBtn.style.padding = '8px 15px';
            clipboardBtn.style.backgroundColor = '#ffc107';
            clipboardBtn.style.color = '#212529';
            clipboardBtn.style.border = 'none';
            clipboardBtn.style.borderRadius = '4px';
            clipboardBtn.style.cursor = 'pointer';
            autoSection.appendChild(clipboardBtn);

            clipboardBtn.addEventListener('click', async () => {
                try {
                    const clipboardText = await navigator.clipboard.readText();
                    log('从剪贴板读取内容', { clipboardText });

                    const parsed = JSON.parse(clipboardText);
                    if (parsed.code && parsed.state && parsed.tenant_url) {
                        log('剪贴板中找到有效授权码', parsed);

                        // 更新显示
                        autoResult.innerHTML = `
                            <strong>✅ 从剪贴板检测到授权码:</strong><br>
                            <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                            <strong>State:</strong> ${parsed.state}<br>
                            <strong>Tenant URL:</strong> ${parsed.tenant_url}<br>
                            <div style="margin:10px 0; padding:8px; background:#e7f3ff; border:1px solid #b3d9ff; border-radius:4px;">
                                <strong>🚀 自动化模式:</strong> 检测到授权码后将自动获取Token<br>
                                <button onclick="autoTriggerTokenGet('${clipboardText}', this.parentElement.parentElement, '剪贴板手动触发')" style="margin-top:5px; padding:5px 10px; background:#007bff; color:white; border:none; border-radius:3px; cursor:pointer;">手动获取Token</button>
                            </div>
                        `;

                        // 自动触发Token获取
                        log('从剪贴板检测到授权码，自动触发Token获取流程');
                        setTimeout(() => {
                            autoTriggerTokenGet(clipboardText, autoResult, '剪贴板自动检测');
                        }, 1000); // 延迟1秒让用户看到检测结果

                    } else {
                        autoResult.innerHTML = '<span style="color:red">剪贴板内容不是有效的授权码JSON</span>';
                    }
                } catch (error) {
                    log('从剪贴板获取失败', { error: error.message });
                    autoResult.innerHTML = `<span style="color:red">从剪贴板获取失败: ${error.message}</span>`;
                }
            });
        }

        // 自动检测授权码
        const authCode = autoExtractCode();
        if (authCode) {
            try {
                const parsed = parseCode(authCode);
                autoResult.innerHTML = `
                    <strong>✅ 检测到授权码:</strong><br>
                    <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                    <strong>State:</strong> ${parsed.state}<br>
                    <strong>Tenant URL:</strong> ${parsed.tenant_url}<br>
                    <div style="margin:10px 0; padding:8px; background:#e7f3ff; border:1px solid #b3d9ff; border-radius:4px;">
                        <strong>🚀 自动化模式:</strong> 检测到授权码后将自动获取Token<br>
                        <button onclick="autoTriggerTokenGet('${authCode}', this.parentElement.parentElement, '手动触发')" style="margin-top:5px; padding:5px 10px; background:#007bff; color:white; border:none; border-radius:3px; cursor:pointer;">手动获取Token</button>
                    </div>
                `;

                // 自动触发Token获取
                log('检测到授权码，自动触发Token获取流程');
                setTimeout(() => {
                    autoTriggerTokenGet(authCode, autoResult, '页面加载自动检测');
                }, 1000); // 延迟1秒让用户看到检测结果

            } catch (error) {
                autoResult.innerHTML = `<span style="color:red">授权码解析失败: ${error.message}</span>`;
            }
        } else {
            autoResult.innerHTML = '<span style="color:#856404;">未检测到授权码</span>';
        }

        // 重新检测按钮事件
        detectBtn.addEventListener('click', () => {
            log('手动触发重新检测');
            autoResult.innerHTML = '<span style="color:blue;">正在重新检测...</span>';

            setTimeout(() => {
                const newAuthCode = autoExtractCode();
                if (newAuthCode) {
                    try {
                        const parsed = parseCode(newAuthCode);
                        autoResult.innerHTML = `
                            <strong>✅ 重新检测到授权码:</strong><br>
                            <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                            <strong>State:</strong> ${parsed.state}<br>
                            <strong>Tenant URL:</strong> ${parsed.tenant_url}<br>
                            <div style="margin:10px 0; padding:8px; background:#e7f3ff; border:1px solid #b3d9ff; border-radius:4px;">
                                <strong>🚀 自动化模式:</strong> 检测到授权码后将自动获取Token<br>
                                <button onclick="autoTriggerTokenGet('${newAuthCode}', this.parentElement.parentElement, '重新检测手动触发')" style="margin-top:5px; padding:5px 10px; background:#007bff; color:white; border:none; border-radius:3px; cursor:pointer;">手动获取Token</button>
                            </div>
                        `;

                        // 自动触发Token获取
                        log('重新检测到授权码，自动触发Token获取流程');
                        setTimeout(() => {
                            autoTriggerTokenGet(newAuthCode, autoResult, '重新检测自动获取');
                        }, 1000); // 延迟1秒让用户看到检测结果

                    } catch (error) {
                        autoResult.innerHTML = `<span style="color:red">授权码解析失败: ${error.message}</span>`;
                    }
                } else {
                    autoResult.innerHTML = '<span style="color:#856404;">仍未检测到授权码</span>';
                }
            }, 500);
        });

        container.appendChild(autoSection);

        log('简化UI创建完成');
        return container;
    }

    // 注册菜单命令
    GM_registerMenuCommand('生成授权链接', handleGenerateAuthLink);

    // 页面加载完成后创建UI
    window.addEventListener('load', () => {
        log('页面加载完成，开始初始化');
        const ui = createSimpleUI();
        document.body.appendChild(ui);
        log('自动化AugmentCode Token Helper已启动');
    });

})();
