// ==UserScript==
// @name         AugmentCode Token Helper
// @namespace    http://tampermonkey.net/
// @version      0.2
// @description  帮助获取AugmentCode的授权码和Token
// <AUTHOR>
// @match        https://auth.augmentcode.com/*
// @match        https://*.api.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_registerMenuCommand
// @grant        GM_openInTab
// ==/UserScript==

(function() {
    'use strict';

    // PKCE相关函数
    function base64URLEncode(buffer) {
        return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    async function sha256Hash(input) {
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return hashBuffer;
    }

    async function createOAuthState() {
        const codeVerifierArray = new Uint8Array(32);
        crypto.getRandomValues(codeVerifierArray);
        const codeVerifier = base64URLEncode(codeVerifierArray.buffer);
        
        const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));
        
        const stateArray = new Uint8Array(8);
        crypto.getRandomValues(stateArray);
        const state = base64URLEncode(stateArray.buffer);

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: Date.now()
        };

        GM_setValue('oauthState', JSON.stringify(oauthState));
        return oauthState;
    }

    // 获取token函数
    async function getAccessToken(tenant_url, codeVerifier, code) {
        return new Promise((resolve, reject) => {
            const clientID = "v";
            const data = {
                grant_type: "authorization_code",
                client_id: clientID,
                code_verifier: codeVerifier,
                redirect_uri: "",
                code: code
            };
            
            GM_xmlhttpRequest({
                method: "POST",
                url: `${tenant_url}token`,
                data: JSON.stringify(data),
                headers: {
                    "Content-Type": "application/json"
                },
                onload: function(response) {
                    try {
                        const json = JSON.parse(response.responseText);
                        const token = json.access_token;
                        resolve(token);
                    } catch (error) {
                        reject(error);
                    }
                },
                onerror: function(error) {
                    reject(error);
                }
            });
        });
    }

    // 生成授权链接
    async function generateAuthorizationUrl() {
        try {
            const oauthState = await createOAuthState();
            const baseUrl = 'https://auth.augmentcode.com/authorize';
            const params = new URLSearchParams({
                client_id: 'v',
                response_type: 'code',
                redirect_uri: '',
                code_challenge: oauthState.codeChallenge,
                code_challenge_method: 'S256',
                state: oauthState.state
            });

            return `${baseUrl}?${params.toString()}`;
        } catch (error) {
            console.error('生成授权链接失败:', error);
            throw error;
        }
    }

    // 处理菜单命令：生成授权链接
    async function handleGenerateAuthLink() {
        try {
            const authUrl = await generateAuthorizationUrl();
            GM_openInTab(authUrl, true); // true表示在后台打开
            alert('授权链接已生成并在新标签页打开！\n\n请在新标签页中完成授权流程。');
        } catch (error) {
            alert(`生成授权链接失败: ${error.message}`);
        }
    }

    // 创建UI
    function createUI() {
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '10px';
        container.style.right = '10px';
        container.style.padding = '10px';
        container.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
        container.style.border = '1px solid #ccc';
        container.style.borderRadius = '5px';
        container.style.zIndex = '10000';
        container.style.width = '300px';
        container.style.maxHeight = '80vh';
        container.style.overflowY = 'auto';
        container.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';

        const title = document.createElement('h3');
        title.textContent = 'AugmentCode Token Helper';
        title.style.margin = '0 0 10px 0';
        container.appendChild(title);

        // 如果是授权页面，添加生成授权链接的按钮
        if (window.location.pathname.includes('/authorize')) {
            const generateBtn = document.createElement('button');
            generateBtn.textContent = '生成PKCE参数';
            generateBtn.style.margin = '5px';
            generateBtn.style.padding = '5px 10px';
            container.appendChild(generateBtn);

            const stateInfo = document.createElement('div');
            stateInfo.style.margin = '10px 0';
            stateInfo.style.wordBreak = 'break-all';
            container.appendChild(stateInfo);

            generateBtn.addEventListener('click', async () => {
                const oauthState = await createOAuthState();
                const authUrl = await generateAuthorizationUrl();
                stateInfo.innerHTML = `
                    <strong>Code Verifier:</strong> <span id="codeVerifier">${oauthState.codeVerifier}</span>
                    <button id="copyVerifier" style="margin-left:5px">复制</button><br>
                    <strong>Code Challenge:</strong> ${oauthState.codeChallenge}<br>
                    <strong>State:</strong> ${oauthState.state}<br><br>
                    <strong>授权链接:</strong><br>
                    <textarea id="authUrl" style="width:100%;height:60px;font-size:12px;" readonly>${authUrl}</textarea><br>
                    <button id="copyAuthUrl" style="margin:5px">复制授权链接</button>
                    <button id="openAuthUrl" style="margin:5px">在新标签页打开</button>
                `;

                document.getElementById('copyVerifier').addEventListener('click', () => {
                    navigator.clipboard.writeText(oauthState.codeVerifier);
                    alert('已复制Code Verifier!');
                });

                document.getElementById('copyAuthUrl').addEventListener('click', () => {
                    navigator.clipboard.writeText(authUrl);
                    alert('已复制授权链接!');
                });

                document.getElementById('openAuthUrl').addEventListener('click', () => {
                    GM_openInTab(authUrl, true);
                    alert('授权链接已在新标签页打开!');
                });
            });
        }

        // 如果是回调页面，添加获取token的功能
        if (window.location.search.includes('code=')) {
            const codeInfo = document.createElement('div');
            codeInfo.style.margin = '10px 0';
            codeInfo.style.wordBreak = 'break-all';
            container.appendChild(codeInfo);
            
            // 从URL中提取授权码
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            
            if (code) {
                codeInfo.innerHTML = `
                    <strong>授权码:</strong> <span id="authCode">${code}</span>
                    <button id="copyCode" style="margin-left:5px">复制</button><br>
                    <strong>State:</strong> ${state}<br>
                `;
                
                document.getElementById('copyCode').addEventListener('click', () => {
                    navigator.clipboard.writeText(code);
                    alert('已复制授权码!');
                });
                
                // 获取token按钮
                const getTokenBtn = document.createElement('button');
                getTokenBtn.textContent = '获取Token';
                getTokenBtn.style.margin = '5px';
                getTokenBtn.style.padding = '5px 10px';
                container.appendChild(getTokenBtn);
                
                const tokenInfo = document.createElement('div');
                tokenInfo.style.margin = '10px 0';
                tokenInfo.style.wordBreak = 'break-all';
                container.appendChild(tokenInfo);
                
                getTokenBtn.addEventListener('click', async () => {
                    try {
                        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
                        if (!savedOAuthState.codeVerifier) {
                            tokenInfo.innerHTML = '<span style="color:red">错误: 未找到保存的Code Verifier，请先在授权页面生成PKCE参数</span>';
                            return;
                        }
                        
                        if (savedOAuthState.state !== state) {
                            tokenInfo.innerHTML = '<span style="color:red">错误: State不匹配，可能存在安全风险</span>';
                            return;
                        }
                        
                        tokenInfo.innerHTML = '<span style="color:blue">正在获取Token...</span>';
                        
                        // 从当前URL获取tenant_url
                        const tenant_url = window.location.origin + '/';
                        
                        const token = await getAccessToken(tenant_url, savedOAuthState.codeVerifier, code);
                        
                        tokenInfo.innerHTML = `
                            <strong>Token:</strong> <span id="accessToken">${token}</span>
                            <button id="copyToken" style="margin-left:5px">复制</button>
                        `;
                        
                        document.getElementById('copyToken').addEventListener('click', () => {
                            navigator.clipboard.writeText(token);
                            alert('已复制Token!');
                        });
                        
                        // 清除保存的状态
                        GM_deleteValue('oauthState');
                        
                    } catch (error) {
                        tokenInfo.innerHTML = `<span style="color:red">错误: ${error.message}</span>`;
                    }
                });
            }
        }

        // 添加简单的Token获取按钮
        const quickTokenBtn = document.createElement('button');
        quickTokenBtn.textContent = '快速获取Token';
        quickTokenBtn.style.margin = '10px 5px';
        quickTokenBtn.style.padding = '8px 15px';
        quickTokenBtn.style.backgroundColor = '#28a745';
        quickTokenBtn.style.color = 'white';
        quickTokenBtn.style.border = 'none';
        quickTokenBtn.style.borderRadius = '4px';
        quickTokenBtn.style.cursor = 'pointer';
        container.appendChild(quickTokenBtn);

        const quickResult = document.createElement('div');
        quickResult.style.margin = '10px 0';
        quickResult.style.wordBreak = 'break-all';
        container.appendChild(quickResult);

        quickTokenBtn.addEventListener('click', async () => {
            const code = '_614fa780615a629281c0d7529801c850';
            const state = '38H5_HxsKLI';
            const tenantUrl = 'https://d13.api.augmentcode.com/';

            try {
                quickResult.innerHTML = '<span style="color:blue">正在获取Token...</span>';

                const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
                console.log('保存的OAuth状态:', savedOAuthState);

                if (savedOAuthState.codeVerifier) {
                    const token = await getAccessToken(tenantUrl, savedOAuthState.codeVerifier, code);

                    quickResult.innerHTML = `
                        <strong>Token:</strong><br>
                        <textarea readonly style="width:100%;height:80px;font-size:11px;">${token}</textarea><br>
                        <button onclick="navigator.clipboard.writeText('${token}');alert('已复制!')">复制Token</button>
                    `;

                    GM_deleteValue('oauthState');
                } else {
                    quickResult.innerHTML = '<span style="color:red">未找到Code Verifier</span>';
                }

            } catch (error) {
                quickResult.innerHTML = `<span style="color:red">错误: ${error.message}</span>`;
            }
        });

        document.body.appendChild(container);
    }

    // 注册菜单命令
    GM_registerMenuCommand('生成授权链接', handleGenerateAuthLink);

    // 页面加载完成后创建UI
    window.addEventListener('load', createUI);
})();