{"name": "augment2api", "version": "1.0.0", "description": "Augment API 客户端工具", "main": "dist/augment2api.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "ts-node augment2api.ts", "build": "tsc", "dev": "ts-node-dev --respawn augment2api.ts"}, "keywords": ["augment", "api", "o<PERSON>h"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^24.0.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "dependencies": {"dotenv": "^16.5.0"}}