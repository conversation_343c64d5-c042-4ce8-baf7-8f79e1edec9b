<!DOCTYPE html>
<html>
<head>
    <title>AugmentCode Token 获取工具</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 4px; }
        .error { background: #ffe6e6; color: #d00; }
        .success { background: #e6ffe6; color: #080; }
    </style>
</head>
<body>
    <div class="container">
        <h1>AugmentCode Token 获取工具</h1>
        
        <div class="form-group">
            <label for="code">授权码 (Code):</label>
            <input type="text" id="code" value="_614fa780615a629281c0d7529801c850">
        </div>
        
        <div class="form-group">
            <label for="state">状态 (State):</label>
            <input type="text" id="state" value="38H5_HxsKLI">
        </div>
        
        <div class="form-group">
            <label for="tenant_url">租户URL (Tenant URL):</label>
            <input type="text" id="tenant_url" value="https://d13.api.augmentcode.com/">
        </div>
        
        <div class="form-group">
            <label for="code_verifier">Code Verifier (如果有的话):</label>
            <input type="text" id="code_verifier" placeholder="如果没有，我们会尝试不使用PKCE">
        </div>
        
        <button onclick="getToken()">获取 Token</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function getToken() {
            const code = document.getElementById('code').value;
            const state = document.getElementById('state').value;
            const tenant_url = document.getElementById('tenant_url').value;
            const code_verifier = document.getElementById('code_verifier').value;
            const resultDiv = document.getElementById('result');
            
            if (!code || !tenant_url) {
                resultDiv.innerHTML = '<div class="result error">请填写授权码和租户URL</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result">正在获取 Token...</div>';
            
            try {
                // 构建请求数据
                const data = {
                    grant_type: "authorization_code",
                    client_id: "v",
                    redirect_uri: "",
                    code: code
                };
                
                // 如果有 code_verifier，添加到请求中
                if (code_verifier) {
                    data.code_verifier = code_verifier;
                }
                
                const response = await fetch(tenant_url + 'token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok && result.access_token) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>成功获取 Token！</h3>
                            <p><strong>Access Token:</strong></p>
                            <textarea readonly style="height: 100px;">${result.access_token}</textarea>
                            <br><br>
                            <button onclick="copyToken('${result.access_token}')">复制 Token</button>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>获取 Token 失败</h3>
                            <p><strong>错误信息:</strong> ${result.error || result.message || '未知错误'}</p>
                            <p><strong>详细信息:</strong> ${result.error_description || JSON.stringify(result)}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>请求失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function copyToken(token) {
            navigator.clipboard.writeText(token).then(() => {
                alert('Token 已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
