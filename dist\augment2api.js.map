{"version": 3, "file": "augment2api.js", "sourceRoot": "", "sources": ["../augment2api.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mCAAiD;AACjD,iEAAmD;AACnD,+CAAgE;AAChE,+CAAiC;AAEjC,WAAW;AACX,IAAI,CAAC;IACH,MAAM,CAAC,MAAM,EAAE,CAAC;AAClB,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;AAC1C,CAAC;AAED,OAAO;AACP,MAAM,MAAM,GAAG;IACb,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,gBAAgB;IACnD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,gCAAgC;IACzE,YAAY,EAAE,8BAA8B;IAC5C,UAAU,EAAE,KAAK,EAAE,QAAQ;CAC5B,CAAC;AAEF;;;;GAIG;AACH,SAAS,eAAe,CAAC,MAAc;IACrC,OAAO,MAAM;SACV,QAAQ,CAAC,QAAQ,CAAC;SAClB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;SACnB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;SACnB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACvB,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CAAC,KAAsB;IACxC,OAAO,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;AACrD,CAAC;AAED;;;GAGG;AACH,SAAS,gBAAgB;IACvB,MAAM,YAAY,GAAG,eAAe,CAAC,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC,CAAC;IACtD,MAAM,aAAa,GAAG,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC7E,MAAM,KAAK,GAAG,eAAe,CAAC,IAAA,oBAAW,EAAC,CAAC,CAAC,CAAC,CAAC;IAE9C,MAAM,UAAU,GAAG;QACjB,YAAY;QACZ,aAAa;QACb,KAAK;QACL,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;KACzB,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,MAAM,oBAAoB,GAAG,CAAC,UAK7B,EAAE,EAAE;IACH,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;QACjC,aAAa,EAAE,MAAM;QACrB,cAAc,EAAE,UAAU,CAAC,aAAa;QACxC,SAAS,EAAE,MAAM,CAAC,QAAQ;QAC1B,KAAK,EAAE,UAAU,CAAC,KAAK;QACvB,MAAM,EAAE,OAAO;QACf,YAAY,EAAE,MAAM,CAAC,WAAW;KACjC,CAAC,CAAC;IACH,MAAM,YAAY,GAAG,IAAI,GAAG,CAC1B,cAAc,MAAM,CAAC,QAAQ,EAAE,EAAE,EACjC,MAAM,CAAC,YAAY,CACpB,CAAC;IACF,OAAO,YAAY,CAAC,QAAQ,EAAE,CAAC;AACjC,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,cAAc,GAAG,CACrB,UAAkB,EAClB,YAAoB,EACpB,IAAY,EACK,EAAE;IACnB,IAAI,CAAC;QACH,MAAM,IAAI,GAAG;YACX,UAAU,EAAE,oBAAoB;YAChC,SAAS,EAAE,MAAM,CAAC,QAAQ;YAC1B,aAAa,EAAE,YAAY;YAC3B,YAAY,EAAE,MAAM,CAAC,WAAW;YAChC,IAAI,EAAE,IAAI;SACX,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAE1E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,UAAU,OAAO,EAAE;YACjD,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;YACD,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC,CAAC;QAEH,YAAY,CAAC,SAAS,CAAC,CAAC;QAExB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,WAAW,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAEhC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACnC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAA,CAAC;AAEF;;;;GAIG;AACH,MAAM,SAAS,GAAG,CAAC,IAAY,EAAuD,EAAE;IACtF,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,UAAU,EAAE,MAAM,CAAC,UAAU;SAC9B,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC,CAAC;AAEF;;;;;GAKG;AACH,SAAe,eAAe,CAAC,UAAkB,EAAE,KAAa,EAAE,OAAe;;;QAC/E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,UAAU,aAAa,EAAE;gBACvD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,aAAa,EAAE,UAAU,KAAK,EAAE;iBACjC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,YAAY,EAAE;wBACZ;4BACE,aAAa,EAAE,iCAAiC;4BAChD,eAAe,EAAE,YAAY;yBAC9B;qBACF;oBACD,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,MAAM;iBACb,CAAC;aACH,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,aAAa,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,MAAM,MAAM,GAAG,MAAA,QAAQ,CAAC,IAAI,0CAAE,SAAS,EAAE,CAAC;YAE1C,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;gBAClC,OAAO,IAAI,EAAE,CAAC;oBACZ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBACnC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;wBAChB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBACrB,MAAM;oBACR,CAAC;oBACD,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC5D,IAAI,CAAC;wBACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;4BACzB,IAAI,CAAC,IAAI;gCAAE,SAAS;4BACpB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBACpB,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;wBACnC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAe,IAAI;;QACjB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YAExC,OAAO;YACP,IAAI,MAAM,CAAC,QAAQ,KAAK,gBAAgB,EAAE,CAAC;gBACzC,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACzD,CAAC;YAED,cAAc;YACd,MAAM,UAAU,GAAG,gBAAgB,EAAE,CAAC;YACtC,MAAM,GAAG,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEjB,QAAQ;YACR,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,KAAK,EAAL,oBAAK,EAAE,MAAM,EAAN,qBAAM,EAAE,CAAC,CAAC;YACvD,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC1C,EAAE,CAAC,KAAK,EAAE,CAAC;YAEX,QAAQ;YACR,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAC1B,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAEvB,OAAO;YACP,IAAI,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YAED,SAAS;YACT,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,cAAc,CAChC,UAAU,CAAC,UAAU,EACrB,UAAU,CAAC,YAAY,EACvB,UAAU,CAAC,IAAI,CAChB,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAExB,OAAO;YACP,MAAM,MAAM,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,KAAK,EAAL,oBAAK,EAAE,MAAM,EAAN,qBAAM,EAAE,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,EAAE,CAAC;YAEf,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACjC,MAAM,eAAe,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAE7D,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CAAA;AAED,OAAO;AACP,IAAI,EAAE,CAAC"}